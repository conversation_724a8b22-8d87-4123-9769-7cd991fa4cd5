#include "ThostFtdcTraderApi.h"
#include "ThostFtdcMdApi.h"
#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
#include <iostream>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <chrono>
#include <memory>
#include <string>

using namespace std;

// 配置结构
struct CTPConfig {
    string trade_front;
    string md_front;
    string broker_id;
    string user_id;
    string password;
    string investor_id;
    string app_id;
    string auth_code;
    string user_product_info;
    string flow_dir;
};

// 交易SPI类 - 专注于交易功能
class TradeSpi : public CThostFtdcTraderSpi {
private:
    CThostFtdcTraderApi* m_api;
    CTPConfig m_config;
    int m_request_id;
    bool m_connected;
    bool m_logged_in;
    bool m_authenticated;
    
    // 线程安全
    mutable mutex m_mutex;
    condition_variable m_cv;
    
public:
    TradeSpi(const CTPConfig& config) 
        : m_config(config), m_request_id(0), m_connected(false), 
          m_logged_in(false), m_authenticated(false) {
        m_api = CThostFtdcTraderApi::CreateFtdcTraderApi(m_config.flow_dir.c_str());
        m_api->RegisterSpi(this);
        m_api->SubscribePublicTopic(THOST_TERT_QUICK);
        m_api->SubscribePrivateTopic(THOST_TERT_QUICK);
    }
    
    ~TradeSpi() {
        if (m_api) {
            m_api->Release();
        }
    }
    
    bool connect() {
        m_api->RegisterFront(const_cast<char*>(m_config.trade_front.c_str()));
        m_api->Init();
        return true;
    }
    
    bool waitForLogin(int timeout_seconds = 30) {
        unique_lock<mutex> lock(m_mutex);
        return m_cv.wait_for(lock, chrono::seconds(timeout_seconds), 
                           [this] { return m_logged_in; });
    }
    
    bool isLoggedIn() const {
        lock_guard<mutex> lock(m_mutex);
        return m_logged_in;
    }
    
    // CTP回调函数
    void OnFrontConnected() override {
        cout << "交易前置连接成功" << endl;
        m_connected = true;
        
        // 发送认证请求
        CThostFtdcReqAuthenticateField req;
        memset(&req, 0, sizeof(req));
        strcpy(req.BrokerID, m_config.broker_id.c_str());
        strcpy(req.UserID, m_config.user_id.c_str());
        strcpy(req.UserProductInfo, m_config.user_product_info.c_str());
        strcpy(req.AuthCode, m_config.auth_code.c_str());
        strcpy(req.AppID, m_config.app_id.c_str());
        
        m_api->ReqAuthenticate(&req, ++m_request_id);
    }
    
    void OnFrontDisconnected(int nReason) override {
        cout << "交易前置断开连接，原因: " << nReason << endl;
        lock_guard<mutex> lock(m_mutex);
        m_connected = false;
        m_logged_in = false;
        m_authenticated = false;
    }
    
    void OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, 
                          CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override {
        if (!isErrorRspInfo(pRspInfo)) {
            cout << "认证成功" << endl;
            m_authenticated = true;
            
            // 发送登录请求
            CThostFtdcReqUserLoginField req;
            memset(&req, 0, sizeof(req));
            strcpy(req.BrokerID, m_config.broker_id.c_str());
            strcpy(req.UserID, m_config.user_id.c_str());
            strcpy(req.Password, m_config.password.c_str());
            
            m_api->ReqUserLogin(&req, ++m_request_id);
        } else {
            cout << "认证失败: " << pRspInfo->ErrorMsg << endl;
        }
    }
    
    void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, 
                       CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override {
        if (!isErrorRspInfo(pRspInfo)) {
            cout << "登录成功" << endl;
            
            // 发送结算确认
            CThostFtdcSettlementInfoConfirmField req;
            memset(&req, 0, sizeof(req));
            strcpy(req.BrokerID, m_config.broker_id.c_str());
            strcpy(req.InvestorID, m_config.investor_id.c_str());
            
            m_api->ReqSettlementInfoConfirm(&req, ++m_request_id);
        } else {
            cout << "登录失败: " << pRspInfo->ErrorMsg << endl;
        }
    }
    
    void OnRspSettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, 
                                   CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override {
        if (!isErrorRspInfo(pRspInfo)) {
            cout << "结算确认成功，交易系统就绪" << endl;
            {
                lock_guard<mutex> lock(m_mutex);
                m_logged_in = true;
            }
            m_cv.notify_all();
        } else {
            cout << "结算确认失败: " << pRspInfo->ErrorMsg << endl;
        }
    }
    
    void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override {
        if (pRspInfo) {
            cout << "收到错误: " << pRspInfo->ErrorID << " - " << pRspInfo->ErrorMsg << endl;
        }
    }
    
    void OnRtnOrder(CThostFtdcOrderField *pOrder) override {
        cout << "报单回报: " << pOrder->InstrumentID << " 状态: " << pOrder->OrderStatus << endl;
    }
    
    void OnRtnTrade(CThostFtdcTradeField *pTrade) override {
        cout << "成交回报: " << pTrade->InstrumentID 
             << " 价格: " << pTrade->Price 
             << " 数量: " << pTrade->Volume << endl;
    }
    
private:
    bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo) {
        return pRspInfo && pRspInfo->ErrorID != 0;
    }
};

// 行情SPI类 - 专注于行情功能
class MdSpi : public CThostFtdcMdSpi {
private:
    CThostFtdcMdApi* m_api;
    CTPConfig m_config;
    int m_request_id;
    bool m_connected;
    bool m_logged_in;
    
    mutable mutex m_mutex;
    condition_variable m_cv;
    
public:
    MdSpi(const CTPConfig& config) 
        : m_config(config), m_request_id(0), m_connected(false), m_logged_in(false) {
        m_api = CThostFtdcMdApi::CreateFtdcMdApi(m_config.flow_dir.c_str());
        m_api->RegisterSpi(this);
    }
    
    ~MdSpi() {
        if (m_api) {
            m_api->Release();
        }
    }
    
    bool connect() {
        m_api->RegisterFront(const_cast<char*>(m_config.md_front.c_str()));
        m_api->Init();
        return true;
    }
    
    bool waitForLogin(int timeout_seconds = 30) {
        unique_lock<mutex> lock(m_mutex);
        return m_cv.wait_for(lock, chrono::seconds(timeout_seconds), 
                           [this] { return m_logged_in; });
    }
    
    void subscribeMarketData(const vector<string>& instruments) {
        if (!m_logged_in) return;
        
        vector<char*> instrument_ids;
        for (const auto& inst : instruments) {
            instrument_ids.push_back(const_cast<char*>(inst.c_str()));
        }
        
        m_api->SubscribeMarketData(instrument_ids.data(), instrument_ids.size());
    }
    
    // CTP回调函数
    void OnFrontConnected() override {
        cout << "行情前置连接成功" << endl;
        m_connected = true;
        
        // 发送登录请求
        CThostFtdcReqUserLoginField req;
        memset(&req, 0, sizeof(req));
        strcpy(req.BrokerID, "10010");  // 行情专用BrokerID
        strcpy(req.UserID, "40090154");
        strcpy(req.Password, "hxqh1234");
        
        m_api->ReqUserLogin(&req, ++m_request_id);
    }
    
    void OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, 
                       CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override {
        if (!isErrorRspInfo(pRspInfo)) {
            cout << "行情登录成功" << endl;
            {
                lock_guard<mutex> lock(m_mutex);
                m_logged_in = true;
            }
            m_cv.notify_all();
        } else {
            cout << "行情登录失败: " << pRspInfo->ErrorMsg << endl;
        }
    }
    
    void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, 
                           CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override {
        if (!isErrorRspInfo(pRspInfo)) {
            cout << "订阅行情成功: " << pSpecificInstrument->InstrumentID << endl;
        }
    }
    
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData) override {
        cout << "行情数据: " << pDepthMarketData->InstrumentID 
             << " 最新价: " << pDepthMarketData->LastPrice 
             << " 时间: " << pDepthMarketData->UpdateTime << endl;
    }
    
    void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override {
        if (pRspInfo) {
            cout << "行情错误: " << pRspInfo->ErrorID << " - " << pRspInfo->ErrorMsg << endl;
        }
    }
    
private:
    bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo) {
        return pRspInfo && pRspInfo->ErrorID != 0;
    }
};

// CTP管理器 - 统一管理交易和行情
class CTPManager {
private:
    unique_ptr<TradeSpi> m_trade_spi;
    unique_ptr<MdSpi> m_md_spi;
    CTPConfig m_config;
    
public:
    CTPManager(const CTPConfig& config) : m_config(config) {
        m_trade_spi = make_unique<TradeSpi>(config);
        m_md_spi = make_unique<MdSpi>(config);
    }
    
    bool initialize() {
        cout << "初始化CTP系统..." << endl;
        
        // 启动交易连接
        if (!m_trade_spi->connect()) {
            cout << "交易连接失败" << endl;
            return false;
        }
        
        // 等待交易登录
        if (!m_trade_spi->waitForLogin()) {
            cout << "交易登录超时" << endl;
            return false;
        }
        
        // 启动行情连接
        if (!m_md_spi->connect()) {
            cout << "行情连接失败" << endl;
            return false;
        }
        
        // 等待行情登录
        if (!m_md_spi->waitForLogin()) {
            cout << "行情登录超时" << endl;
            return false;
        }
        
        // 订阅行情
        vector<string> instruments = {"IO2508-C-3550", "IH2409", "CF305"};
        m_md_spi->subscribeMarketData(instruments);
        
        cout << "CTP系统初始化完成" << endl;
        return true;
    }
    
    TradeSpi* getTradeSpi() { return m_trade_spi.get(); }
    MdSpi* getMdSpi() { return m_md_spi.get(); }
};

int main() {
    // 配置参数
    CTPConfig config;
    config.trade_front = "tcp://192.168.193.58:8721";
    config.md_front = "tcp://180.169.30.185:31213";
    config.broker_id = "99999999";
    config.user_id = "200900138522";
    config.password = "000000";
    config.investor_id = "200900138522";
    config.app_id = "TEST_FAPCLIENT_1.0.0";
    config.auth_code = "SPUYO5IJSTJZNK1O";
    config.user_product_info = "";
    config.flow_dir = "./flow";
    
    // 创建CTP管理器
    CTPManager ctp_manager(config);
    
    // 初始化系统
    if (!ctp_manager.initialize()) {
        cout << "CTP系统初始化失败" << endl;
        return -1;
    }
    
    cout << "系统运行中，按回车键退出..." << endl;
    cin.get();
    
    return 0;
}
