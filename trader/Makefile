# CTP统一管理系统 Makefile

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2 -fPIC

# 包含路径（需要根据实际CTP SDK路径调整）
INCLUDES = -I./lib/headers -I.

# 库路径和链接库（需要根据实际CTP SDK路径调整）
LIBPATH = -L./lib
LIBS = -lthosttraderapi_se -lthostmduserapi_se -lpthread

# 源文件
WEBSERVICE_SOURCES = ctp_webservice.cpp
MDSERVICE_SOURCES = ctp_mdservice.cpp
MANAGER_SOURCES = ctp_manager.cpp
DIRECT_INTERFACE_SOURCES = ctp_direct_interface.cpp

# 目标文件
WEBSERVICE_OBJS = $(WEBSERVICE_SOURCES:.cpp=.o)
MDSERVICE_OBJS = $(MDSERVICE_SOURCES:.cpp=.o)
MANAGER_OBJS = $(MANAGER_SOURCES:.cpp=.o)
DIRECT_INTERFACE_OBJS = $(DIRECT_INTERFACE_SOURCES:.cpp=.o)

# 可执行文件
UNIFIED_EXAMPLE = ctp_unified_example
BEST_PRACTICE = ctp_best_practice

# 动态库
WEBSERVICE_LIB = libctp_webservice.so

# 默认目标
all: $(WEBSERVICE_LIB) $(UNIFIED_EXAMPLE) $(BEST_PRACTICE)

# 编译动态库
$(WEBSERVICE_LIB): $(WEBSERVICE_OBJS) $(MDSERVICE_OBJS) $(MANAGER_OBJS) $(DIRECT_INTERFACE_OBJS)
	$(CXX) -shared -o $@ $^ $(LIBPATH) $(LIBS)

# 编译统一示例程序
$(UNIFIED_EXAMPLE): ctp_unified_example.o $(WEBSERVICE_OBJS) $(MDSERVICE_OBJS) $(MANAGER_OBJS)
	$(CXX) -o $@ $^ $(LIBPATH) $(LIBS)

# 编译最佳实践示例
$(BEST_PRACTICE): ctp_best_practice.o
	$(CXX) -o $@ $^ $(LIBPATH) $(LIBS)

# 编译目标文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f *.o $(WEBSERVICE_LIB) $(UNIFIED_EXAMPLE) $(BEST_PRACTICE)

# 安装（可选）
install: $(WEBSERVICE_LIB)
	cp $(WEBSERVICE_LIB) /usr/local/lib/
	ldconfig

# 创建必要的目录
setup:
	mkdir -p trade_flow md_flow flow

# 运行统一示例
run-unified: $(UNIFIED_EXAMPLE) setup
	LD_LIBRARY_PATH=./lib:$$LD_LIBRARY_PATH ./$(UNIFIED_EXAMPLE)

# 运行最佳实践示例
run-best: $(BEST_PRACTICE) setup
	LD_LIBRARY_PATH=./lib:$$LD_LIBRARY_PATH ./$(BEST_PRACTICE)

# 调试版本
debug: CXXFLAGS += -g -DDEBUG
debug: all

# 帮助信息
help:
	@echo "可用目标:"
	@echo "  all          - 编译所有目标"
	@echo "  clean        - 清理编译文件"
	@echo "  setup        - 创建必要的目录"
	@echo "  run-unified  - 运行统一管理示例"
	@echo "  run-best     - 运行最佳实践示例"
	@echo "  debug        - 编译调试版本"
	@echo "  install      - 安装动态库到系统"
	@echo "  help         - 显示此帮助信息"

.PHONY: all clean install setup run-unified run-best debug help
