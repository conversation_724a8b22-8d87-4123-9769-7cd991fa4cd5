#include "ctp_manager.h"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>

// 如果有yaml配置文件支持，可以包含yaml库
// #include <yaml-cpp/yaml.h>

CTPManager::CTPManager() : m_initialized(false) {
    m_trade_service = std::make_unique<CTPWebService>();
    m_md_service = std::make_unique<CTPMdService>();
}

CTPManager::~CTPManager() {
    stopAllServices();
}

bool CTPManager::initialize(const CTPUnifiedConfig& config) {
    m_config = config;
    m_initialized = true;
    
    std::cout << "CTP管理器初始化完成" << std::endl;
    return true;
}

bool CTPManager::initializeFromConfigFiles(const std::string& env_config_path, 
                                          const std::string& trade_config_path) {
    if (!loadConfigFromFiles(env_config_path, trade_config_path)) {
        std::cerr << "加载配置文件失败" << std::endl;
        return false;
    }
    
    return initialize(m_config);
}

bool CTPManager::startTradeService() {
    if (!m_initialized) {
        std::cerr << "管理器未初始化" << std::endl;
        return false;
    }
    
    std::cout << "启动交易服务..." << std::endl;
    return m_trade_service->initialize(m_config.trade_config);
}

bool CTPManager::startMdService() {
    if (!m_initialized) {
        std::cerr << "管理器未初始化" << std::endl;
        return false;
    }
    
    std::cout << "启动行情服务..." << std::endl;
    return m_md_service->initialize(m_config.md_config);
}

bool CTPManager::startAllServices() {
    std::cout << "启动所有CTP服务..." << std::endl;
    
    // 先启动交易服务
    if (!startTradeService()) {
        std::cerr << "交易服务启动失败" << std::endl;
        return false;
    }
    
    // 等待交易服务就绪
    if (!waitForTradeReady()) {
        std::cerr << "交易服务就绪超时" << std::endl;
        return false;
    }
    
    // 启动行情服务
    if (!startMdService()) {
        std::cerr << "行情服务启动失败" << std::endl;
        return false;
    }
    
    // 等待行情服务就绪
    if (!waitForMdReady()) {
        std::cerr << "行情服务就绪超时" << std::endl;
        return false;
    }
    
    // 订阅默认合约
    if (!m_config.default_instruments.empty()) {
        std::cout << "订阅默认合约..." << std::endl;
        subscribeMarketData(m_config.default_instruments);
    }
    
    std::cout << "所有CTP服务启动完成" << std::endl;
    return true;
}

void CTPManager::stopTradeService() {
    // CTPWebService的析构函数会自动释放资源
    std::cout << "交易服务已停止" << std::endl;
}

void CTPManager::stopMdService() {
    // CTPMdService的析构函数会自动释放资源
    std::cout << "行情服务已停止" << std::endl;
}

void CTPManager::stopAllServices() {
    stopTradeService();
    stopMdService();
    std::cout << "所有CTP服务已停止" << std::endl;
}

bool CTPManager::isTradeReady() const {
    return m_trade_service && m_trade_service->isLoggedIn();
}

bool CTPManager::isMdReady() const {
    return m_md_service && m_md_service->isLoggedIn();
}

bool CTPManager::isAllReady() const {
    return isTradeReady() && isMdReady();
}

bool CTPManager::waitForTradeReady(int timeout_seconds) {
    auto start_time = std::chrono::steady_clock::now();
    while (!isTradeReady()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time).count();
        if (elapsed >= timeout_seconds) {
            return false;
        }
    }
    return true;
}

bool CTPManager::waitForMdReady(int timeout_seconds) {
    return m_md_service->waitForLogin(timeout_seconds);
}

bool CTPManager::waitForAllReady(int timeout_seconds) {
    int half_timeout = timeout_seconds / 2;
    return waitForTradeReady(half_timeout) && waitForMdReady(half_timeout);
}

// 便捷接口实现
bool CTPManager::placeOrder(const std::string& instrument_id, double price, int volume, 
                           const std::string& direction, const std::string& offset_flag, 
                           const std::string& exchange_id) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    char dir = (direction == "buy" || direction == "BUY") ? THOST_FTDC_D_Buy : THOST_FTDC_D_Sell;
    char offset = THOST_FTDC_OF_Open;
    if (offset_flag == "close" || offset_flag == "CLOSE") {
        offset = THOST_FTDC_OF_Close;
    } else if (offset_flag == "closetoday" || offset_flag == "CLOSETODAY") {
        offset = THOST_FTDC_OF_CloseToday;
    } else if (offset_flag == "closeyesterday" || offset_flag == "CLOSEYESTERDAY") {
        offset = THOST_FTDC_OF_CloseYesterday;
    }
    
    return m_trade_service->orderInsertComplete(instrument_id.c_str(), price, volume, 
                                               dir, offset, exchange_id.c_str());
}

bool CTPManager::cancelOrder(const std::string& order_ref, const std::string& instrument_id, 
                            const std::string& exchange_id) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->orderActionComplete(order_ref.c_str(), instrument_id.c_str(), 
                                               exchange_id.c_str());
}

bool CTPManager::queryAccount(std::vector<CThostFtdcTradingAccountField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryTradingAccountComplete(results);
}

bool CTPManager::queryPosition(const std::string& instrument_id, 
                              std::vector<CThostFtdcInvestorPositionField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryInvestorPositionComplete(instrument_id.c_str(), results);
}

bool CTPManager::queryInstrument(const std::string& instrument_id, const std::string& exchange_id, 
                                std::vector<CThostFtdcInstrumentField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryInstrumentComplete(instrument_id.c_str(), exchange_id.c_str(), results);
}

bool CTPManager::subscribeMarketData(const std::vector<std::string>& instruments) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->subscribeMarketDataComplete(instruments);
}

bool CTPManager::unsubscribeMarketData(const std::vector<std::string>& instruments) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->unsubscribeMarketData(instruments);
}

bool CTPManager::getLatestMarketData(const std::string& instrument_id, 
                                    CThostFtdcDepthMarketDataField& data) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->getLatestMarketData(instrument_id, data);
}

std::vector<std::string> CTPManager::getSubscribedInstruments() {
    if (!isMdReady()) {
        return {};
    }
    
    return m_md_service->getSubscribedInstruments();
}

// 回调设置
void CTPManager::setOrderCallback(CTPWebService::OrderCallbackDirect callback) {
    m_trade_service->setOrderCallbackDirect(callback);
}

void CTPManager::setTradeCallback(CTPWebService::TradeCallbackDirect callback) {
    m_trade_service->setTradeCallbackDirect(callback);
}

void CTPManager::setMarketDataCallback(CTPMdService::MarketDataCallback callback) {
    m_md_service->setMarketDataCallback(callback);
}

void CTPManager::setSubscribeCallback(CTPMdService::SubscribeCallback callback) {
    m_md_service->setSubscribeCallback(callback);
}

// 内部辅助方法
bool CTPManager::loadConfigFromFiles(const std::string& env_config_path, 
                                    const std::string& trade_config_path) {
    // 这里应该实现从配置文件加载配置的逻辑
    // 由于没有yaml库，这里提供一个简化的硬编码配置示例
    
    // 交易配置
    strcpy(m_config.trade_config.trade_front, "tcp://192.168.193.58:8721");
    strcpy(m_config.trade_config.broker_id, "99999999");
    strcpy(m_config.trade_config.user_id, "200900138522");
    strcpy(m_config.trade_config.password, "000000");
    strcpy(m_config.trade_config.investor_id, "200900138522");
    strcpy(m_config.trade_config.app_id, "TEST_FAPCLIENT_1.0.0");
    strcpy(m_config.trade_config.auth_code, "SPUYO5IJSTJZNK1O");
    strcpy(m_config.trade_config.user_product_info, "");
    strcpy(m_config.trade_config.flow_dir, "./trade_flow");
    
    // 行情配置
    strcpy(m_config.md_config.md_front, "tcp://180.169.30.185:31213");
    strcpy(m_config.md_config.broker_id, "10010");  // 行情专用BrokerID
    strcpy(m_config.md_config.user_id, "40090154");
    strcpy(m_config.md_config.password, "hxqh1234");
    strcpy(m_config.md_config.flow_dir, "./md_flow");
    
    // 默认订阅合约
    m_config.default_instruments = {"IO2508-C-3550", "IH2409", "CF305"};
    
    std::cout << "配置加载完成（使用硬编码配置）" << std::endl;
    return true;
}
