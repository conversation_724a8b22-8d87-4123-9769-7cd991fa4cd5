#include "ctp_webservice.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <cstring>

CTPWebService::CTPWebService()
    : m_api(nullptr), m_request_id(0),
      m_order_ref(0), m_connected(false), m_logged_in(false), m_response_ready(false),
      m_order_callback_direct(nullptr), m_trade_callback_direct(nullptr) {
}

CTPWebService::~CTPWebService() {
    if (m_api) {
        m_api->Release();
    }
}

bool CTPWebService::initialize(const CTPConnectionConfig& config) {
    // 保存配置
    m_config = config;

    // 创建API实例
    m_api = CThostFtdcTraderApi::CreateFtdcTraderApi(m_config.flow_dir);

    // 注册回调
    m_api->RegisterSpi(this);
    m_api->SubscribePublicTopic(THOST_TERT_QUICK);
    m_api->SubscribePrivateTopic(THOST_TERT_QUICK);

    // 连接前置机
    m_api->RegisterFront(const_cast<char*>(m_config.trade_front));
    m_api->Init();

    return true;
}

void CTPWebService::setOrderCallbackDirect(OrderCallbackDirect callback) {
    m_order_callback_direct = callback;
}

void CTPWebService::setTradeCallbackDirect(TradeCallbackDirect callback) {
    m_trade_callback_direct = callback;
}

void CTPWebService::setResponse(bool success, const std::string& error_msg) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_current_response.success = success;
    m_current_response.error_msg = error_msg;
    m_response_ready = true;
    m_cv.notify_all();
}

void CTPWebService::clearResults() {
    m_position_results.clear();
    m_account_results.clear();
    m_instrument_results.clear();
    m_trade_results.clear();
    m_order_results.clear();

    resetResponseState();
}

void CTPWebService::resetResponseState() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_response_ready = false;
}

// 回调函数实现
void CTPWebService::OnFrontConnected() {
    std::cout << "连接成功，开始认证..." << std::endl;
    
    CThostFtdcReqAuthenticateField req;
    memset(&req, 0, sizeof(req));
    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.UserID, m_config.user_id);
    strcpy(req.UserProductInfo, m_config.user_product_info);
    strcpy(req.AuthCode, m_config.auth_code);
    strcpy(req.AppID, m_config.app_id);
    
    m_api->ReqAuthenticate(&req, ++m_request_id);
    m_connected = true;
}

void CTPWebService::OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, 
                                     CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "认证成功，开始登录..." << std::endl;
        
        CThostFtdcReqUserLoginField req;
        memset(&req, 0, sizeof(req));
        strcpy(req.BrokerID, m_config.broker_id);
        strcpy(req.UserID, m_config.user_id);
        strcpy(req.Password, m_config.password);
        
        m_api->ReqUserLogin(&req, ++m_request_id);
    }
}

void CTPWebService::OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, 
                                  CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "登录成功，确认结算信息..." << std::endl;
        
        CThostFtdcSettlementInfoConfirmField req;
        memset(&req, 0, sizeof(req));
        strcpy(req.BrokerID, m_config.broker_id);
        strcpy(req.InvestorID, m_config.investor_id);
        
        m_api->ReqSettlementInfoConfirm(&req, ++m_request_id);
    }
}

void CTPWebService::OnRspSettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, 
                                               CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "结算信息确认成功，系统就绪" << std::endl;
        m_logged_in = true;
        // 移除m_cv.notify_all()调用
    }
}

void CTPWebService::OnRspQryTradingAccount(CThostFtdcTradingAccountField *pTradingAccount, 
                                          CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pTradingAccount && !isErrorRspInfo(pRspInfo)) {
        m_account_results.push_back(*pTradingAccount);
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryInvestorPosition(CThostFtdcInvestorPositionField *pInvestorPosition, 
                                            CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pInvestorPosition && !isErrorRspInfo(pRspInfo)) {
        m_position_results.push_back(*pInvestorPosition);
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryInstrument(CThostFtdcInstrumentField *pInstrument, 
                                      CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pInstrument && !isErrorRspInfo(pRspInfo)) {
        m_instrument_results.push_back(*pInstrument);
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryTrade(CThostFtdcTradeField *pTrade, 
                                 CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pTrade && !isErrorRspInfo(pRspInfo)) {
        m_trade_results.push_back(*pTrade);
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

bool CTPWebService::isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo) {
    return pRspInfo && pRspInfo->ErrorID != 0;
}

void CTPWebService::waitForResponse(int timeout_seconds) {
    std::unique_lock<std::mutex> lock(m_mutex);
    m_cv.wait_for(lock, std::chrono::seconds(timeout_seconds), 
                  [this] { return m_response_ready; });
}

void CTPWebService::OnFrontDisconnected(int nReason) {
    m_connected = false;
    m_logged_in = false;
    std::cout << "前置机连接断开，原因: " << nReason << std::endl;
}

void CTPWebService::OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pRspInfo) {
        std::cout << "收到错误响应: " << pRspInfo->ErrorID 
                  << " - " << pRspInfo->ErrorMsg << std::endl;
    }
}

void CTPWebService::OnRspQryOrder(CThostFtdcOrderField *pOrder, 
                                  CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pOrder && !isErrorRspInfo(pRspInfo)) {
        m_order_results.push_back(*pOrder);
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo),
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData, 
                                           CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo),
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryInstrumentMarginRate(CThostFtdcInstrumentMarginRateField *pInstrumentMarginRate,
                                                CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRspQryInstrumentCommissionRate(CThostFtdcInstrumentCommissionRateField *pInstrumentCommissionRate,
                                                    CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPWebService::OnRtnOrder(CThostFtdcOrderField *pOrder) {
    if (m_order_callback_direct && pOrder) {
        // 直接传递CTP原生结构，不进行转换
        m_order_callback_direct(pOrder);
    }
}

void CTPWebService::OnRtnTrade(CThostFtdcTradeField *pTrade) {
    if (m_trade_callback_direct && pTrade) {
        // 直接传递CTP原生结构，不进行转换
        m_trade_callback_direct(pTrade);
    }
}

void CTPWebService::OnRspOrderInsert(CThostFtdcInputOrderField *pInputOrder,
                                     CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pRspInfo && pRspInfo->ErrorID != 0) {
        setResponse(false, pRspInfo->ErrorMsg);
    } else {
        setResponse(true, "报单录入成功");
    }
}

void CTPWebService::OnRspOrderAction(CThostFtdcInputOrderActionField *pInputOrderAction,
                                     CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pRspInfo && pRspInfo->ErrorID != 0) {
        setResponse(false, pRspInfo->ErrorMsg);
    } else {
        setResponse(true, "撤单操作成功");
    }
}

// 开仓/平仓交易
bool CTPWebService::reqOrderInsert(const char* instrument_id, double price, int volume, 
                                  char direction, char offset_flag, const char* exchange_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法下单" << std::endl;
        return false;
    }
    
    CThostFtdcInputOrderField req;
    memset(&req, 0, sizeof(req));
    
    // 设置账户相关信息
    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);
    strcpy(req.UserID, m_config.user_id);
    
    // 设置合约信息
    strcpy(req.InstrumentID, instrument_id);
    if (exchange_id && strlen(exchange_id) > 0) {
        strcpy(req.ExchangeID, exchange_id);
    }
    
    // 设置报单引用
    sprintf(req.OrderRef, "%d", ++m_order_ref);
    
    // 设置交易方向和开平标志
    req.Direction = direction;
    req.CombOffsetFlag[0] = offset_flag;
    
    // 设置价格和数量
    req.VolumeTotalOriginal = volume;
    
    // 设置默认值
    if(price > 0) {
        req.LimitPrice = price;
        req.OrderPriceType = THOST_FTDC_OPT_LimitPrice;     // 限价单
    } else {
        req.OrderPriceType = THOST_FTDC_OPT_AnyPrice;      // 市价单
    }
        
    req.TimeCondition = THOST_FTDC_TC_GFD;              // 当日有效
    req.VolumeCondition = THOST_FTDC_VC_AV;             // 任何数量
    req.MinVolume = 1;                                  // 最小成交量
    req.ContingentCondition = THOST_FTDC_CC_Immediately; // 立即触发
    req.ForceCloseReason = THOST_FTDC_FCC_NotForceClose; // 非强平
    req.CombHedgeFlag[0] = THOST_FTDC_HF_Speculation;    // 投机
    req.IsAutoSuspend = 0;                              // 非自动挂起
    req.UserForceClose = 0;                             // 非用户强平
    
    // 发送报单请求
    int rt = m_api->ReqOrderInsert(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "报单请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "报单请求已发送，合约: " << instrument_id 
              << ", 方向: " << (direction == THOST_FTDC_D_Buy ? "买入" : "卖出")
              << ", 开平: " << (offset_flag == THOST_FTDC_OF_Open ? "开仓" : "平仓")
              << ", 价格: " << price
              << ", 数量: " << volume
              << ", 报单引用: " << req.OrderRef << std::endl;
    
    return true;
}

// 撤单操作
bool CTPWebService::reqOrderAction(const char* order_ref, const char* instrument_id, const char* exchange_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法撤单" << std::endl;
        return false;
    }
    
    CThostFtdcInputOrderActionField req;
    memset(&req, 0, sizeof(req));
    
    // 设置账户相关信息
    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);
    strcpy(req.UserID, m_config.user_id);
    
    // 设置撤单信息
    strcpy(req.OrderRef, order_ref);
    strcpy(req.InstrumentID, instrument_id);
    if (exchange_id && strlen(exchange_id) > 0) {
        strcpy(req.ExchangeID, exchange_id);
    }
    
    // 设置撤单标志
    req.ActionFlag = THOST_FTDC_AF_Delete;
    
    // 发送撤单请求
    int rt = m_api->ReqOrderAction(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "撤单请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "撤单请求已发送，报单引用: " << order_ref 
              << ", 合约: " << instrument_id << std::endl;
    
    return true;
}

// 查询资金账户
bool CTPWebService::reqQryTradingAccount() {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询资金账户" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    CThostFtdcQryTradingAccountField req;
    memset(&req, 0, sizeof(req));
    
    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);
    
    int rt = m_api->ReqQryTradingAccount(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "查询资金账户请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "查询资金账户请求已发送" << std::endl;
    return true;
}

// 查询投资者持仓
bool CTPWebService::reqQryInvestorPosition(const char* instrument_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询持仓" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    CThostFtdcQryInvestorPositionField req;
    memset(&req, 0, sizeof(req));
    
    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);
    
    if (instrument_id && strlen(instrument_id) > 0) {
        strcpy(req.InstrumentID, instrument_id);
    }
    
    int rt = m_api->ReqQryInvestorPosition(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "查询持仓请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "查询持仓请求已发送" << std::endl;
    return true;
}

// 查询成交
bool CTPWebService::reqQryTrade(const char* instrument_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询成交" << std::endl;
        return false;
    }

    // 清理之前的结果
    clearResults();

    CThostFtdcQryTradeField req;
    memset(&req, 0, sizeof(req));

    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);

    if (instrument_id && strlen(instrument_id) > 0) {
        strcpy(req.InstrumentID, instrument_id);
    }

    int rt = m_api->ReqQryTrade(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "查询成交请求发送失败，错误码: " << rt << std::endl;
        return false;
    }

    std::cout << "查询成交请求已发送" << std::endl;
    return true;
}

// 查询报单
bool CTPWebService::reqQryOrder(const char* instrument_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询报单" << std::endl;
        return false;
    }

    // 清理之前的结果
    clearResults();

    CThostFtdcQryOrderField req;
    memset(&req, 0, sizeof(req));

    strcpy(req.BrokerID, m_config.broker_id);
    strcpy(req.InvestorID, m_config.investor_id);

    if (instrument_id && strlen(instrument_id) > 0) {
        strcpy(req.InstrumentID, instrument_id);
    }

    int rt = m_api->ReqQryOrder(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "查询报单请求发送失败，错误码: " << rt << std::endl;
        return false;
    }

    std::cout << "查询报单请求已发送" << std::endl;
    return true;
}

// 查询合约
bool CTPWebService::reqQryInstrument(const char* instrument_id, const char* exchange_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询合约" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    CThostFtdcQryInstrumentField req;
    memset(&req, 0, sizeof(req));
    
    if (instrument_id && strlen(instrument_id) > 0) {
        strcpy(req.InstrumentID, instrument_id);
    }
    
    if (exchange_id && strlen(exchange_id) > 0) {
        strcpy(req.ExchangeID, exchange_id);
    }
    
    int rt = m_api->ReqQryInstrument(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "查询合约请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "查询合约请求已发送" << std::endl;
    return true;
}

// 完整交易接口实现 - 请求+等待+获取结果
bool CTPWebService::queryTradingAccountComplete(std::vector<CThostFtdcTradingAccountField>& results) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询资金账户" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    // 发送查询请求
    if (!reqQryTradingAccount()) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 获取结果
    results = m_account_results;
    return !results.empty();
}

bool CTPWebService::queryInstrumentComplete(const char* instrument_id, const char* exchange_id, 
                                           std::vector<CThostFtdcInstrumentField>& results) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询合约信息" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    // 发送查询请求
    if (!reqQryInstrument(instrument_id, exchange_id)) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 获取结果
    results = m_instrument_results;
    return !results.empty();
}

bool CTPWebService::queryInvestorPositionComplete(const char* instrument_id, 
                                                 std::vector<CThostFtdcInvestorPositionField>& results) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询持仓信息" << std::endl;
        return false;
    }
    
    // 清理之前的结果
    clearResults();
    
    // 发送查询请求
    if (!reqQryInvestorPosition(instrument_id)) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 获取结果
    results = m_position_results;
    return !results.empty();
}

bool CTPWebService::queryTradeComplete(const char* instrument_id, std::vector<CThostFtdcTradeField>& results) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询成交信息" << std::endl;
        return false;
    }

    // 清理之前的结果
    clearResults();

    // 发送查询请求
    if (!reqQryTrade(instrument_id)) {
        return false;
    }

    // 等待响应
    waitForResponse();

    // 获取结果
    results = m_trade_results;
    return !results.empty();
}

bool CTPWebService::queryOrderComplete(const char* instrument_id, std::vector<CThostFtdcOrderField>& results) {
    if (!m_logged_in) {
        std::cout << "未登录，无法查询报单信息" << std::endl;
        return false;
    }

    // 清理之前的结果
    clearResults();

    // 发送查询请求
    if (!reqQryOrder(instrument_id)) {
        return false;
    }

    // 等待响应
    waitForResponse();

    // 获取结果
    results = m_order_results;
    return !results.empty();
}

// 完整交易操作接口实现
bool CTPWebService::orderInsertComplete(const char* instrument_id, double price, int volume, 
                                       char direction, char offset_flag, const char* exchange_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法下单" << std::endl;
        return false;
    }
    
    // 重置响应状态
    resetResponseState();
    
    // 发送下单请求
    if (!reqOrderInsert(instrument_id, price, volume, direction, offset_flag, exchange_id)) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 返回操作结果
    return m_current_response.success;
}

bool CTPWebService::orderActionComplete(const char* order_ref, const char* instrument_id, 
                                       const char* exchange_id) {
    if (!m_logged_in) {
        std::cout << "未登录，无法撤单" << std::endl;
        return false;
    }
    
    // 重置响应状态
    resetResponseState();
    
    // 发送撤单请求
    if (!reqOrderAction(order_ref, instrument_id, exchange_id)) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 返回操作结果
    return m_current_response.success;
}
