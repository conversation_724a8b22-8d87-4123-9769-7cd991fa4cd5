
#include <QtCore/QCoreApplication>
#include <QDebug>



#include "ThostFtdcMdApi.h"
#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
static int requestId = 0;

#include <iostream>
using namespace std;

#include <Windows.h>
#include <thread>

#include <algorithm>
#include <functional>
#include <limits>
static int requestID = 0; // 请求编号

static int instrument_size = 0;

// 会话参数
TThostFtdcFrontIDType	trade_front_id;	//前置编号
TThostFtdcSessionIDType	session_id;	//会话编号,
TThostFtdcOrderRefType	order_ref;	//报单引用

class CustomTradeSpi : public CThostFtdcMdSpi
{
	// ---- ctp_api部分回调接口 ---- //
public:
	CustomTradeSpi(CThostFtdcMdApi * api) { m_api = api; }
	~CustomTradeSpi() {}

	///当客户端与交易后台建立起通信连接时（还未登录前），该方法被调用。
	virtual void OnFrontConnected() ;
	///登录请求响应
	virtual void OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) ;
	///登出请求响应
	virtual void OnRspUserLogout(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) ;
	///订阅行情应答
	virtual void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast);

	///深度行情通知
	virtual void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData);

	///错误应答
	virtual void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
public:
	CThostFtdcMdApi * m_api;
	bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo); // 是否收到错误信息
};

///当客户端与交易后台建立起通信连接时（还未登录前），该方法被调用。
void CustomTradeSpi::OnFrontConnected()
{
	cout << "OnFrontConnected" << endl;

	//连接成功后 进行登录
	CThostFtdcReqUserLoginField loginReq;
	memset(&loginReq, 0, sizeof(loginReq));


	strcpy(loginReq.BrokerID, "10010");
	strcpy(loginReq.UserID, "40090154");
	strcpy(loginReq.Password, "hxqh1234");

	int rt = m_api->ReqUserLogin(&loginReq, ++requestId);
}

void CustomTradeSpi::OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	int i = 0;
}

void CustomTradeSpi::OnRspUserLogout(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspUserLogout" << endl;
}

///登录请求响应
void CustomTradeSpi::OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspUserLogin" << endl;

	//登录成功后开始订阅
	//virtual int SubscribeMarketData(char* ppInstrumentID[], int nCount) = 0;

	//char* m_pInstrumentID[50000];
	//string dm = "IF2405";

	//CThostFtdcUserLogoutField loginReq;
	//memset(&loginReq, 0, sizeof(CThostFtdcUserLogoutField));
	//strcpy(loginReq.BrokerID, "10010");
	//strcpy(loginReq.UserID, "40090154");

	//m_api->ReqUserLogout(&loginReq, ++requestId);;

	char *ppInstrumentID[] = {"IO2508-C-3550"};
	m_api->SubscribeMarketData(ppInstrumentID,1);

	//m_pInstrumentID[0] = new char[strlen(dm.c_str()) + 1];
	//strcpy(m_pInstrumentID[0], dm.c_str());

	//m_api->SubscribeMarketData(m_pInstrumentID, 1);

	//char *ppInstrumentID1[100] = { "IH2303" };
	//m_api->SubscribeMarketData(ppInstrumentID1, 1);

	//char *ppInstrumentID2[100] = { "IH2309" };
	//m_api->SubscribeMarketData(ppInstrumentID2, 1);
}

///订阅行情应答
void CustomTradeSpi::OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspSubMarketData" << endl;
}

///深度行情通知
void CustomTradeSpi::OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData)
{
	cout << "OnRtnDepthMarketData LastPrice:" << pDepthMarketData->LastPrice << ":InstrumentID:" << pDepthMarketData->InstrumentID <<  endl;

	if (pDepthMarketData->AskPrice5 == (numeric_limits<double>::max)() )
	{
		pDepthMarketData->AskPrice5 = 0;
	}
}

int main(int argc, char *argv[])
{
	QCoreApplication a(argc, argv);

	int right = 16;
	int ab = right & 8;

	CThostFtdcMdApi *g_pMdApi = nullptr;           // 交易指针
	g_pMdApi = CThostFtdcMdApi::CreateFtdcMdApi(); // 创建交易实例
	CustomTradeSpi pMdSpi(g_pMdApi);               // 创建交易回调实例
	g_pMdApi->RegisterSpi(&pMdSpi);                // 注册事件类
	g_pMdApi->RegisterFront("tcp://**************:31213");
	g_pMdApi->Init();
	g_pMdApi->Join();

	int i = 0;
	return a.exec();
}