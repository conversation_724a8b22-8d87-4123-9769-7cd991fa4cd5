﻿#include "ThostFtdcTraderApi.h"
#include "ThostFtdcMdApi.h"
#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
static int requestId = 0;

#include <string.h>

#include <iostream>
#include <limits>
using namespace std;

#include <unistd.h>
//#include <Windows.h>
#include <thread>

static int requestID = 0; // 请求编号

static int instrument_size = 0;

// 会话参数
TThostFtdcFrontIDType	trade_front_id;	//前置编号
TThostFtdcSessionIDType	session_id;	//会话编号
TThostFtdcOrderRefType	order_ref;	//报单引用

string UserID = "200900138522";
string BrokerID = "99999999";

// 行情登录信息
string MdUserID = "40090154";
string MdBrokerID = "10010";
string MdPassword = "hxqh1234";

class CustomTradeSpi : public CThostFtdcTraderSpi, public CThostFtdcMdSpi
{
	// ---- ctp_api部分回调接口 ---- //
public:
	CustomTradeSpi(CThostFtdcTraderApi * tradeApi, CThostFtdcMdApi * mdApi) {
		m_tradeApi = tradeApi;
		m_mdApi = mdApi;
	}
	~CustomTradeSpi() {}

	// ---- 交易API回调接口 ---- //
	///当客户端与交易后台建立起通信连接时（还未登录前），该方法被调用。
	void OnFrontConnected();

	void OnFrontDisconnected(int nReason);

	///登录请求响应
	void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询合约响应
	void OnRspQryInstrument(CThostFtdcInstrumentField *pInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询行情响应
	void OnRspQryDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///投资者结算结果确认响应
	void OnRspSettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///客户端认证响应
	void OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	void reqSettlementInfoConfirm();

	void reqOrderInsert();
	void reqOrderInsert1();
	void reqOrderAction();

	void AddMainContract(const char* instrumnet, const char* month, double PreOpenInterest, double ddailymaxprice, double dailyminprice);

	///报单通知
	void OnRtnOrder(CThostFtdcOrderField *pOrder);

	///成交通知
	void OnRtnTrade(CThostFtdcTradeField *pTrade);

	///报单录入请求响应
	void OnRspOrderInsert(CThostFtdcInputOrderField *pInputOrder, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///报单操作请求响应
	void OnRspOrderAction(CThostFtdcInputOrderActionField *pInputOrderAction, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询合约保证金率响应
	virtual void OnRspQryInstrumentMarginRate(CThostFtdcInstrumentMarginRateField *pInstrumentMarginRate, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询合约手续费率响应
	virtual void OnRspQryInstrumentCommissionRate(CThostFtdcInstrumentCommissionRateField *pInstrumentCommissionRate, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///错误应答
	virtual void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	///请求查询资金账户响应
	virtual void OnRspQryTradingAccount(CThostFtdcTradingAccountField *pTradingAccount, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询投资者持仓响应
	virtual void OnRspQryInvestorPosition(CThostFtdcInvestorPositionField *pInvestorPosition, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	///请求查询成交响应
	virtual void OnRspQryTrade(CThostFtdcTradeField *pTrade, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) ;

	// ---- 行情API回调接口 ---- //
	///订阅行情应答
	virtual void OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast);
	///深度行情通知
	virtual void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData);

public:
	CThostFtdcTraderApi * m_tradeApi;  // 交易API
	CThostFtdcMdApi * m_mdApi;         // 行情API
	bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo); // 是否收到错误信息

	void reqAuth();
	void reqQueryInstrument();
	void reqMdLogin();  // 行情登录
	void reqSubscribeMarketData();  // 订阅行情

	int reqQryTradingAccount();
	int reqQryInvestorPosition();
	int reqQryDeal();
};



int CustomTradeSpi::reqQryDeal()
{

	CThostFtdcQryTradeField pQryTrade;
	memset(&pQryTrade, 0, sizeof(CThostFtdcQryTradeField));
	strcpy(pQryTrade.BrokerID, BrokerID.c_str());
	strcpy(pQryTrade.InvestorID, UserID.c_str());
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqQryTrade(&pQryTrade, ++requestID);
	if (!rt)
		std::cout << ">>>>>>请求查询成交请求成功" << std::endl;
	else
		std::cerr << "--->>>请求查询成交请求失败" << std::endl;
	return 0;
}

int CustomTradeSpi::reqQryInvestorPosition()
{
	//CThostFtdcQryInvestorPositionField *pQryInvestorPosition,

	CThostFtdcQryInvestorPositionField pQryInvestorPosition;
	memset(&pQryInvestorPosition, 0, sizeof(CThostFtdcQryInvestorPositionField));
	strcpy(pQryInvestorPosition.BrokerID, BrokerID.c_str());
	strcpy(pQryInvestorPosition.InvestorID, UserID.c_str());
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqQryInvestorPosition(&pQryInvestorPosition, ++requestID);
	if (!rt)
		std::cout << ">>>>>>发送投资者结算结果确认请求成功" << std::endl;
	else
		std::cerr << "--->>>发送投资者结算结果确认请求失败" << std::endl;
	return 0;
}


///请求查询资金账户
int CustomTradeSpi::reqQryTradingAccount()
{
	//CThostFtdcQryTradingAccountField *pQryTradingAccount, int nRequestID

	CThostFtdcQryTradingAccountField pQryTradingAccountReq;
	memset(&pQryTradingAccountReq, 0, sizeof(CThostFtdcQryTradingAccountField));
	strcpy(pQryTradingAccountReq.BrokerID, BrokerID.c_str());
	strcpy(pQryTradingAccountReq.InvestorID, UserID.c_str());
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqQryTradingAccount(&pQryTradingAccountReq, ++requestID);
	if (!rt)
		std::cout << ">>>>>>发送投资者结算结果确认请求成功" << std::endl;
	else
		std::cerr << "--->>>发送投资者结算结果确认请求失败" << std::endl;
	return 0;
}


void CustomTradeSpi::OnFrontConnected()
{
	// 由于交易API和行情API都会调用这个函数，需要区分处理
	// 这里我们通过检查当前线程或其他方式来区分
	// 简单起见，我们先启动交易认证，然后在交易登录成功后启动行情登录
	cout << "前置连接成功" << endl;
	reqAuth();  // 先进行交易认证
}


void CustomTradeSpi::OnFrontDisconnected(int nReason)
{
	cout << "OnFrontDisconnected" << endl;
}

void CustomTradeSpi::reqAuth()
{
	CThostFtdcReqAuthenticateField AuthenReq;
	memset(&AuthenReq, 0, sizeof(CThostFtdcReqAuthenticateField));

	strcpy(AuthenReq.BrokerID, BrokerID.c_str());
	strcpy(AuthenReq.UserID, UserID.c_str());
	strcpy(AuthenReq.UserProductInfo, "");
	strcpy(AuthenReq.AuthCode, "SPUYO5IJSTJZNK1O");
	strcpy(AuthenReq.AppID, "TEST_FAPCLIENT_1.0.0");

	int rt = m_tradeApi->ReqAuthenticate(&AuthenReq, requestID);

	if (!rt)
		std::cout << ">>>>>>发送认证请求成功" << std::endl;
	else
		std::cerr << "--->>>发送认证请求失败" << std::endl;
}

// 行情登录请求
void CustomTradeSpi::reqMdLogin()
{
	CThostFtdcReqUserLoginField loginReq;
	memset(&loginReq, 0, sizeof(loginReq));

	strcpy(loginReq.BrokerID, MdBrokerID.c_str());
	strcpy(loginReq.UserID, MdUserID.c_str());
	strcpy(loginReq.Password, MdPassword.c_str());

	int rt = m_mdApi->ReqUserLogin(&loginReq, ++requestId);
	if (!rt)
		std::cout << ">>>>>>发送行情登录请求成功" << std::endl;
	else
		std::cerr << "--->>>发送行情登录请求失败" << std::endl;
}


///请求查询资金账户响应
void CustomTradeSpi::OnRspQryTradingAccount(CThostFtdcTradingAccountField *pTradingAccount, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspQryTradingAccount" << endl;
	int i = 0;
}


///请求查询成交响应
void CustomTradeSpi::OnRspQryTrade(CThostFtdcTradeField *pTrade, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspQryTrade" << endl;

	int i = 0;
}


void CustomTradeSpi::OnRspQryInvestorPosition(CThostFtdcInvestorPositionField *pInvestorPosition, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	cout << "OnRspQryInvestorPosition" << endl;
	/*
	cout << "OnRspQryInvestorPosition ExchangeID:" << pInvestorPosition->ExchangeID 
		<< " :InstrumentID:" << pInvestorPosition->InstrumentID  << endl;

	string   InstrumentID(pInvestorPosition->InstrumentID);
	if (InstrumentID=="IH2309")
	{
		int i = 0;
	}
	*/

}


///客户端认证响应
void CustomTradeSpi::OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	if (!isErrorRspInfo(pRspInfo))
	{
		std::cout << "=====认证成功=====" << std::endl;
		// 开始登录
		CThostFtdcReqUserLoginField loginReq;
		memset(&loginReq, 0, sizeof(loginReq));


		strcpy(loginReq.BrokerID, BrokerID.c_str());
		strcpy(loginReq.UserID, UserID.c_str());
		strcpy(loginReq.Password, "000000");

		int rt = m_tradeApi->ReqUserLogin(&loginReq, ++requestId);

	}
	else
	{
		std::cerr << "返回错误--->>> ErrorID=" << pRspInfo->ErrorID << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
	}
}

void CustomTradeSpi::OnRspUserLogin(
	CThostFtdcRspUserLoginField *pRspUserLogin,
	CThostFtdcRspInfoField *pRspInfo,
	int nRequestID,
	bool bIsLast)
{
	if (!isErrorRspInfo(pRspInfo))
	{
		// 通过BrokerID来区分是交易登录还是行情登录
		string brokerID(pRspUserLogin->BrokerID);

		if (brokerID == BrokerID)  // 交易登录
		{
			std::cout << "=====交易登录成功=====" << UserID.c_str() << std::endl;
			std::cout << "交易日： " << pRspUserLogin->TradingDay << std::endl;
			std::cout << "登录时间： " << pRspUserLogin->LoginTime << std::endl;
			std::cout << "经纪商： " << pRspUserLogin->BrokerID << std::endl;
			std::cout << "帐户名： " << pRspUserLogin->UserID << std::endl;

			// 保存会话参数
			trade_front_id = pRspUserLogin->FrontID;
			session_id = pRspUserLogin->SessionID;
			//strcpy(order_ref, pRspUserLogin->MaxOrderRef);

			// 投资者结算结果确认
			reqSettlementInfoConfirm();

			// 交易登录成功后，启动行情登录
			reqMdLogin();
		}
		else if (brokerID == MdBrokerID)  // 行情登录
		{
			std::cout << "=====行情登录成功=====" << std::endl;
			std::cout << "交易日： " << pRspUserLogin->TradingDay << std::endl;
			std::cout << "登录时间： " << pRspUserLogin->LoginTime << std::endl;

			// 登录成功后订阅行情
			reqSubscribeMarketData();
		}
	}
	else
	{
		std::cerr << "登录失败--->>> ErrorID=" << pRspInfo->ErrorID << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
	}
}



// 订阅行情
void CustomTradeSpi::reqSubscribeMarketData()
{
	// 订阅多个合约的行情
	char *ppInstrumentID[] = {"IO2508-C-3550", "IH2409", "CF305"};
	int count = sizeof(ppInstrumentID) / sizeof(ppInstrumentID[0]);

	int rt = m_mdApi->SubscribeMarketData(ppInstrumentID, count);
	if (!rt)
		std::cout << ">>>>>>发送订阅行情请求成功" << std::endl;
	else
		std::cerr << "--->>>发送订阅行情请求失败" << std::endl;
}
void CustomTradeSpi::OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	std::cerr << "收到错误响应--->>> ErrorID=" << pRspInfo->ErrorID << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
}

// 订阅行情应答
void CustomTradeSpi::OnRspSubMarketData(CThostFtdcSpecificInstrumentField* pSpecificInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
	if (!isErrorRspInfo(pRspInfo))
	{
		std::cout << "订阅行情成功: " << pSpecificInstrument->InstrumentID << std::endl;
	}
	else
	{
		std::cerr << "订阅行情失败: " << pSpecificInstrument->InstrumentID << std::endl;
	}
}

// 深度行情通知
void CustomTradeSpi::OnRtnDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData)
{
	std::cout << "收到行情数据 - 合约: " << pDepthMarketData->InstrumentID
			  << ", 最新价: " << pDepthMarketData->LastPrice
			  << ", 买一价: " << pDepthMarketData->BidPrice1
			  << ", 卖一价: " << pDepthMarketData->AskPrice1
			  << ", 成交量: " << pDepthMarketData->Volume
			  << ", 更新时间: " << pDepthMarketData->UpdateTime << std::endl;

	// 处理无效价格
	if (pDepthMarketData->AskPrice5 == (numeric_limits<double>::max)())
	{
		pDepthMarketData->AskPrice5 = 0;
	}
}



void CustomTradeSpi::reqQueryInstrument()
{
	CThostFtdcQryInstrumentField instrumentReq;
	memset(&instrumentReq, 0, sizeof(instrumentReq));
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqQryInstrument(&instrumentReq, ++requestID);
	if (!rt)
		std::cout << ">>>>>>发送合约查询请求成功" << std::endl;
	else
		std::cerr << "--->>>发送合约查询请求失败" << std::endl;
}



bool CustomTradeSpi::isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo)
{
	bool bResult = pRspInfo && (pRspInfo->ErrorID != 0);
	if (bResult)
	{
		std::cerr << "返回错误--->>> ErrorID=" << pRspInfo->ErrorID << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
		//WriteLog("返回错误--->>> ErrorID=%d,ErrorMsg=%s\n", pRspInfo->ErrorID, pRspInfo->ErrorMsg);
	}
	return bResult;
}


void CustomTradeSpi::OnRspOrderInsert(
	CThostFtdcInputOrderField *pInputOrder,
	CThostFtdcRspInfoField *pRspInfo,
	int nRequestID,
	bool bIsLast)
{
	if (!isErrorRspInfo(pRspInfo))
	{
		std::cout << "=====报单录入成功=====" << std::endl;
		std::cout << "合约代码： " << pInputOrder->InstrumentID << std::endl;
		std::cout << "价格： " << pInputOrder->LimitPrice << std::endl;
		std::cout << "数量： " << pInputOrder->VolumeTotalOriginal << std::endl;
		std::cout << "开仓方向： " << pInputOrder->Direction << std::endl;
	}
}

void CustomTradeSpi::OnRspOrderAction(
	CThostFtdcInputOrderActionField *pInputOrderAction,
	CThostFtdcRspInfoField *pRspInfo,
	int nRequestID,
	bool bIsLast)
{
	if (!isErrorRspInfo(pRspInfo))
	{
		std::cout << "=====报单操作成功=====" << std::endl;
		std::cout << "合约代码： " << pInputOrderAction->InstrumentID << std::endl;
		std::cout << "操作标志： " << pInputOrderAction->ActionFlag;
	}
}

void CustomTradeSpi::OnRtnOrder(CThostFtdcOrderField *pOrder)
{

	cout << "--->>> OnRtnOrder:";

	char str[10];
	sprintf(str, "%d", pOrder->OrderSubmitStatus);
	int orderState = atoi(str) - 48;	//报单状态0=已经提交，3=已经接受



										//if (isMyOrder(pOrder))
										//{
	std::cout << "=====收到报单应答=====" << "委托号:" << pOrder->OrderSysID << std::endl;
	//	WriteLog("=====收到报单应答===== 委托号:%s\n", pOrder->OrderSysID);
	//if (isTradingOrder(pOrder))
	//{
	std::cout << "--->>> 报单状态:" << pOrder->OrderStatus << std::endl;
	if (pOrder->OrderStatus == THOST_FTDC_OST_Unknown)
		std::cout << "--->>> 状态未知 ！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OSS_CancelRejected)
		std::cout << "--->>> 撤单被拒 ！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OSS_InsertRejected)
		std::cout << "--->>> 报单被拒！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OSS_InsertSubmitted)
		std::cout << "--->>> 已经提交！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OSS_Accepted)
		std::cout << "--->>> 已经接受！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OST_AllTraded)
		std::cout << "--->>> 全部成交！" << std::endl;
	else if (pOrder->OrderStatus == THOST_FTDC_OST_Canceled)
		std::cout << "--->>> 已撤单！" << std::endl;
	//}
	//}
}

void CustomTradeSpi::OnRtnTrade(CThostFtdcTradeField *pTrade)
{
	std::cout << "=====报单成功成交=====" << std::endl;
	std::cout << "成交时间： " << pTrade->TradeTime << std::endl;
	std::cout << "合约代码： " << pTrade->InstrumentID << std::endl;
	std::cout << "成交价格： " << pTrade->Price << std::endl;
	std::cout << "成交量： " << pTrade->Volume << std::endl;
	std::cout << "开平仓方向： " << pTrade->Direction << std::endl;
}




void CustomTradeSpi::OnRspSettlementInfoConfirm(
	CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm,
	CThostFtdcRspInfoField *pRspInfo,
	int nRequestID,
	bool bIsLast)
{
	//if (!isErrorRspInfo(pRspInfo))
	//{
	std::cout << "=====投资者结算结果确认成功=====" << std::endl;
	std::cout << "确认日期： " << pSettlementInfoConfirm->ConfirmDate << std::endl;
	std::cout << "确认时间： " << pSettlementInfoConfirm->ConfirmTime << std::endl;

	//reqQueryInstrument();
	//reqOrderInsert();  //开
	//reqOrderInsert1();//平仓
	//reqQryInvestorPosition();
	//reqOrderAction();
	//reqQryDeal();
}





void CustomTradeSpi::reqSettlementInfoConfirm()
{
	CThostFtdcSettlementInfoConfirmField settlementConfirmReq;
	memset(&settlementConfirmReq, 0, sizeof(settlementConfirmReq));
	strcpy(settlementConfirmReq.BrokerID, BrokerID.c_str());
	strcpy(settlementConfirmReq.InvestorID, UserID.c_str());
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqSettlementInfoConfirm(&settlementConfirmReq, ++requestID);
	if (!rt)
		std::cout << ">>>>>>发送投资者结算结果确认请求成功" << std::endl;
	else
		std::cerr << "--->>>发送投资者结算结果确认请求失败reqSettlementInfoConfirm" << std::endl;
}



void CustomTradeSpi::reqOrderInsert1()
{
	CThostFtdcInputOrderField orderInsertReq;
	memset(&orderInsertReq, 0, sizeof(orderInsertReq));
	///经纪公司代码
	strcpy(orderInsertReq.BrokerID, BrokerID.c_str());
	///投资者代码
	strcpy(orderInsertReq.InvestorID, UserID.c_str());
	///投资者代码
	strcpy(orderInsertReq.UserID, UserID.c_str());
	///合约代码
	strcpy(orderInsertReq.InstrumentID, "CF305");
	///报单引用
	int orderRef = atol(order_ref);
	sprintf(order_ref, "%d", orderRef + 1);
	strcpy(orderInsertReq.OrderRef, order_ref);
	///报单价格条件: 限价
	orderInsertReq.OrderPriceType = THOST_FTDC_OPT_LimitPrice;
	///买卖方向: 
	orderInsertReq.Direction = THOST_FTDC_D_Sell;
	///组合开平标志: 开仓
	orderInsertReq.CombOffsetFlag[0] = THOST_FTDC_OF_Close;
	///组合投机套保标志
	orderInsertReq.CombHedgeFlag[0] = THOST_FTDC_HF_Speculation;
	///价格
	orderInsertReq.LimitPrice = 14285;
	///数量：1
	orderInsertReq.VolumeTotalOriginal = 1;
	///有效期类型: 当日有效
	orderInsertReq.TimeCondition = THOST_FTDC_TC_GFD;
	///成交量类型: 任何数量
	orderInsertReq.VolumeCondition = THOST_FTDC_VC_AV;
	///最小成交量: 1
	orderInsertReq.MinVolume = 1;
	///触发条件: 立即
	orderInsertReq.ContingentCondition = THOST_FTDC_CC_Immediately;
	///强平原因: 非强平
	orderInsertReq.ForceCloseReason = THOST_FTDC_FCC_NotForceClose;
	///自动挂起标志: 否
	orderInsertReq.IsAutoSuspend = 0;
	///用户强评标志: 否
	orderInsertReq.UserForceClose = 0;

	//strcpy(orderInsertReq.ExchangeID, "CFFEX");
	//strcpy(orderInsertReq.ExchangeID, "SHFE");
	strcpy(orderInsertReq.ExchangeID, "CZCE");
	//static int requestID = 0; // 请求编号

	int rt = m_tradeApi->ReqOrderInsert(&orderInsertReq, ++requestID);
	if (!rt)
	{
		std::cout << ">>>>>>发送报单录入请求成功" << std::endl;  //2989496
													   //WriteLog(">>>>>>发送报单录入请求成功\n");
	}
	else
	{
		std::cerr << "--->>>发送报单录入请求失败" << std::endl;
		//WriteLog("--->>>发送报单录入请求失败\n");
	}
}




void CustomTradeSpi::reqOrderInsert()
{
	CThostFtdcInputOrderField orderInsertReq;
	memset(&orderInsertReq, 0, sizeof(orderInsertReq));
	///经纪公司代码
	strcpy(orderInsertReq.BrokerID, BrokerID.c_str());
	///投资者代码
	strcpy(orderInsertReq.InvestorID, UserID.c_str());
	///投资者代码
	strcpy(orderInsertReq.UserID, UserID.c_str());
	///合约代码
	strcpy(orderInsertReq.InstrumentID, "IH2409");
	///报单引用
	int orderRef = atol(order_ref);
	sprintf(order_ref, "%d", orderRef + 1);

	//strcpy(orderInsertReq.OrderRef, order_ref);
	strcpy(orderInsertReq.OrderRef, "112");
	///报单价格条件: 限价
	orderInsertReq.OrderPriceType = THOST_FTDC_OPT_LimitPrice;
	///买卖方向: 
	orderInsertReq.Direction = THOST_FTDC_D_Buy;
	///组合开平标志: 开仓
	orderInsertReq.CombOffsetFlag[0] = THOST_FTDC_OF_Open;
	///组合投机套保标志
	orderInsertReq.CombHedgeFlag[0] = THOST_FTDC_HF_Speculation;
	///价格
	orderInsertReq.LimitPrice = 2391;
	///数量：1
	orderInsertReq.VolumeTotalOriginal = 1;
	///有效期类型: 当日有效
	orderInsertReq.TimeCondition = THOST_FTDC_TC_GFD;
	///成交量类型: 任何数量
	orderInsertReq.VolumeCondition = THOST_FTDC_VC_AV;
	///最小成交量: 1
	orderInsertReq.MinVolume = 1;
	///触发条件: 立即
	orderInsertReq.ContingentCondition = THOST_FTDC_CC_Immediately;
	///强平原因: 非强平
	orderInsertReq.ForceCloseReason = THOST_FTDC_FCC_NotForceClose;
	///自动挂起标志: 否
	orderInsertReq.IsAutoSuspend = 0;
	///用户强评标志: 否
	orderInsertReq.UserForceClose = 0;



	strcpy(orderInsertReq.ExchangeID, "CFFEX");
	//strcpy(orderInsertReq.ExchangeID, "SHFE");
	//strcpy(orderInsertReq.ExchangeID, "CZCE");
	//static int requestID = 0; // 请求编号

	int rt = m_tradeApi->ReqOrderInsert(&orderInsertReq, ++requestID);
	if (!rt)
	{
		std::cout << ">>>>>>发送报单录入请求成功" << std::endl;  //2989496
													   //WriteLog(">>>>>>发送报单录入请求成功\n");
	}
	else
	{
		std::cerr << "--->>>发送报单录入请求失败" << std::endl;
		//WriteLog("--->>>发送报单录入请求失败\n");
	}
}



void CustomTradeSpi::reqOrderAction()
{

	CThostFtdcInputOrderActionField orderActionReq;
	memset(&orderActionReq, 0, sizeof(orderActionReq));
	///经纪公司代码
	strcpy(orderActionReq.BrokerID, BrokerID.c_str());
	///投资者代码
	strcpy(orderActionReq.InvestorID, UserID.c_str());
	///报单引用
	//int orderRef = atol(order_ref);
	//sprintf(order_ref, "%d", orderRef + 1);
	//strcpy(orderActionReq.OrderRef, order_ref);
	///报单操作引用
	//	TThostFtdcOrderActionRefType	OrderActionRef;	
	///请求编号
	//	TThostFtdcRequestIDType	RequestID;
	///前置编号
	orderActionReq.FrontID = 1;
	///会话编号
	orderActionReq.SessionID = 119;
	///报单引用
	strcpy(orderActionReq.OrderRef,"112");  //报单引用
	///交易所代码
	strcpy(orderActionReq.ExchangeID, "CFFEX");
	///报单编号
	//strcpy(orderActionReq.OrderSysID, "314IF23093632");
	///操作标志
	orderActionReq.ActionFlag = THOST_FTDC_AF_Delete;
	///价格
	//	TThostFtdcPriceType	LimitPrice;
	///数量变化
	//	TThostFtdcVolumeType	VolumeChange;
	///用户代码
	strcpy(orderActionReq.UserID, UserID.c_str());
	///合约代码
	strcpy(orderActionReq.InstrumentID, "IH2409");
	//static int requestID = 0; // 请求编号
	int rt = m_tradeApi->ReqOrderAction(&orderActionReq, ++requestID);
	if (!rt)
		std::cout << ">>>>>>发送报单操作请求成功" << std::endl;
	else
		std::cerr << "--->>>发送报单操作请求失败" << std::endl;
}


///请求查询行情响应
void CustomTradeSpi::OnRspQryDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	//合约名
	char instrument[3];
	//合约月份
	char month[5];
	//合约代码长度
	int strsize = strlen(pDepthMarketData->InstrumentID);
	//不同交易所合约代码不一样
	//TA605/IF1601/l1605/M605
	switch (strsize)
	{
	case 6:
	{
		instrument[0] = pDepthMarketData->InstrumentID[0];
		instrument[1] = pDepthMarketData->InstrumentID[1];
		instrument[2] = '\0';
		month[0] = pDepthMarketData->InstrumentID[2];
		month[1] = pDepthMarketData->InstrumentID[3];
		month[2] = pDepthMarketData->InstrumentID[4];
		month[3] = pDepthMarketData->InstrumentID[5];
		month[4] = '\0';
		AddMainContract(instrument, month, pDepthMarketData->PreOpenInterest, pDepthMarketData->UpperLimitPrice, pDepthMarketData->LowerLimitPrice);
		break;

	}
	case 5:
	{
		if (pDepthMarketData->InstrumentID[1] == '1' || pDepthMarketData->InstrumentID[1] == '2') {
			instrument[0] = pDepthMarketData->InstrumentID[0];
			instrument[1] = '\0';
			month[0] = pDepthMarketData->InstrumentID[1];
			month[1] = pDepthMarketData->InstrumentID[2];
			month[2] = pDepthMarketData->InstrumentID[3];
			month[3] = pDepthMarketData->InstrumentID[4];
			month[4] = '\0';
			AddMainContract(instrument, month, pDepthMarketData->PreOpenInterest, pDepthMarketData->UpperLimitPrice, pDepthMarketData->LowerLimitPrice);
			break;
		}
		else {
			instrument[0] = pDepthMarketData->InstrumentID[0];
			instrument[1] = pDepthMarketData->InstrumentID[1];
			instrument[2] = '\0';
			month[0] = pDepthMarketData->InstrumentID[2];
			month[1] = pDepthMarketData->InstrumentID[3];
			month[2] = pDepthMarketData->InstrumentID[4];
			month[3] = '\0';
			AddMainContract(instrument, month, pDepthMarketData->PreOpenInterest, pDepthMarketData->UpperLimitPrice, pDepthMarketData->LowerLimitPrice);
			break;;
		}

	}
	case 4:
	{
		instrument[0] = pDepthMarketData->InstrumentID[0];
		instrument[1] = '\0';
		month[0] = pDepthMarketData->InstrumentID[1];
		month[1] = pDepthMarketData->InstrumentID[2];
		month[2] = pDepthMarketData->InstrumentID[3];
		month[3] = '\0';
		AddMainContract(instrument, month, pDepthMarketData->PreOpenInterest, pDepthMarketData->UpperLimitPrice, pDepthMarketData->LowerLimitPrice);
		break;

	}
	default:
		break;
	}
	//查询结束
	if (bIsLast)
	{
		//m_bIsQueryIng = false;
		//m_bHaveQueryDominantContract = true;
		//SendMsg(QString::fromLocal8Bit("查询市场主力合约结束"));
		int i = 0;
	}

	//qDebug() << "ID:" << pDepthMarketData->InstrumentID ;
	//string tmp(pDepthMarketData->InstrumentID);
}




//添加主力合约
void CustomTradeSpi::AddMainContract(const char* instrumnet, const char* month, double PreOpenInterest, double ddailymaxprice, double dailyminprice)
{

}

///请求查询合约响应
void CustomTradeSpi::OnRspQryInstrument(CThostFtdcInstrumentField *pInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	int i = 0;
}



///请求查询合约保证金率响应
void  CustomTradeSpi::OnRspQryInstrumentMarginRate(CThostFtdcInstrumentMarginRateField *pInstrumentMarginRate, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{


}

///请求查询合约手续费率响应
void  CustomTradeSpi::OnRspQryInstrumentCommissionRate(CThostFtdcInstrumentCommissionRateField *pInstrumentCommissionRate, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{

}




double Random(int start,int end)
{
	int dis = end - start;
	return (start + dis*(rand() / (RAND_MAX + 1.0)));
}




bool test()
{	
	int a=8;
	if(a>10)
	{
	 return false;
	}

	int i=0;
}







int main(int argc, char *argv[])
{
	bool a=test();
	if(!a)
	{
	cout << "false" << endl;
	}
	else
	{
	cout << "true" << endl;
	}

	// 创建交易API
	CThostFtdcTraderApi *g_pTradeUserApi = nullptr;
	g_pTradeUserApi = CThostFtdcTraderApi::CreateFtdcTraderApi("./trade_flow");

	// 创建行情API
	CThostFtdcMdApi *g_pMdApi = nullptr;
	g_pMdApi = CThostFtdcMdApi::CreateFtdcMdApi("./md_flow");

	// 创建统一的回调实例，同时处理交易和行情
	CustomTradeSpi pTradeSpi(g_pTradeUserApi, g_pMdApi);

	// 注册交易API回调
	g_pTradeUserApi->RegisterSpi(&pTradeSpi);
	g_pTradeUserApi->SubscribePublicTopic(THOST_TERT_QUICK);
	g_pTradeUserApi->SubscribePrivateTopic(THOST_TERT_QUICK);
	g_pTradeUserApi->RegisterFront("tcp://**************:8721");

	// 注册行情API回调
	g_pMdApi->RegisterSpi(&pTradeSpi);
	g_pMdApi->RegisterFront("tcp://**************:31213");

	// 启动交易API
	g_pTradeUserApi->Init();

	// 启动行情API
	g_pMdApi->Init();

	std::cout << "交易和行情系统已启动，按任意键退出..." << std::endl;

	// 等待用户输入
	getchar();

	// 释放资源
	g_pTradeUserApi->Release();
	g_pMdApi->Release();

	return 0;
}
