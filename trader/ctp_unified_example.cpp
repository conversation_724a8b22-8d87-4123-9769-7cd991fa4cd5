#include "ctp_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <sstream>

// 回调函数示例
void onOrderCallback(const CThostFtdcOrderField* order_data) {
    std::cout << "📋 报单回报: " << order_data->InstrumentID 
              << " 状态: " << order_data->OrderStatus 
              << " 报单编号: " << order_data->OrderRef << std::endl;
}

void onTradeCallback(const CThostFtdcTradeField* trade_data) {
    std::cout << "💰 成交回报: " << trade_data->InstrumentID 
              << " 价格: " << trade_data->Price 
              << " 数量: " << trade_data->Volume 
              << " 成交编号: " << trade_data->TradeID << std::endl;
}

void onMarketDataCallback(const CThostFtdcDepthMarketDataField* market_data) {
    std::cout << "📈 行情数据: " << market_data->InstrumentID 
              << " 最新价: " << market_data->LastPrice
              << " 买一: " << market_data->BidPrice1 
              << " 卖一: " << market_data->AskPrice1
              << " 时间: " << market_data->UpdateTime << std::endl;
}

void onSubscribeCallback(const char* instrument_id, bool success, const char* error_msg) {
    if (success) {
        std::cout << "✅ 订阅成功: " << instrument_id << std::endl;
    } else {
        std::cout << "❌ 订阅失败: " << instrument_id << " 错误: " << error_msg << std::endl;
    }
}

void printMenu() {
    std::cout << "\n=== CTP统一管理系统 ===" << std::endl;
    std::cout << "1. 查询账户资金" << std::endl;
    std::cout << "2. 查询持仓" << std::endl;
    std::cout << "3. 查询合约" << std::endl;
    std::cout << "4. 下单" << std::endl;
    std::cout << "5. 撤单" << std::endl;
    std::cout << "6. 订阅行情" << std::endl;
    std::cout << "7. 取消订阅" << std::endl;
    std::cout << "8. 查看最新行情" << std::endl;
    std::cout << "9. 查看已订阅合约" << std::endl;
    std::cout << "0. 退出" << std::endl;
    std::cout << "请选择操作: ";
}

int main() {
    std::cout << "启动CTP统一管理系统..." << std::endl;
    
    // 创建CTP管理器
    CTPManager ctp_manager;
    
    // 初始化配置（这里使用硬编码配置，实际使用中可以从配置文件加载）
    if (!ctp_manager.initializeFromConfigFiles()) {
        std::cerr << "初始化失败" << std::endl;
        return -1;
    }
    
    // 设置回调函数
    ctp_manager.setOrderCallback(onOrderCallback);
    ctp_manager.setTradeCallback(onTradeCallback);
    ctp_manager.setMarketDataCallback(onMarketDataCallback);
    ctp_manager.setSubscribeCallback(onSubscribeCallback);
    
    // 启动所有服务
    if (!ctp_manager.startAllServices()) {
        std::cerr << "服务启动失败" << std::endl;
        return -1;
    }
    
    std::cout << "🎉 CTP系统启动成功！" << std::endl;
    
    // 主循环
    int choice;
    while (true) {
        printMenu();
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                // 查询账户资金
                std::cout << "查询账户资金..." << std::endl;
                std::vector<CThostFtdcTradingAccountField> accounts;
                if (ctp_manager.queryAccount(accounts)) {
                    for (const auto& account : accounts) {
                        std::cout << "账户ID: " << account.AccountID 
                                  << " 可用资金: " << account.Available
                                  << " 总资产: " << account.Balance << std::endl;
                    }
                } else {
                    std::cout << "查询失败" << std::endl;
                }
                break;
            }
            
            case 2: {
                // 查询持仓
                std::cout << "请输入合约代码（留空查询所有）: ";
                std::string instrument_id;
                std::cin.ignore();
                std::getline(std::cin, instrument_id);
                
                std::vector<CThostFtdcInvestorPositionField> positions;
                if (ctp_manager.queryPosition(instrument_id, positions)) {
                    for (const auto& pos : positions) {
                        std::cout << "合约: " << pos.InstrumentID 
                                  << " 方向: " << pos.PosiDirection
                                  << " 持仓: " << pos.Position
                                  << " 今仓: " << pos.TodayPosition << std::endl;
                    }
                } else {
                    std::cout << "查询失败" << std::endl;
                }
                break;
            }
            
            case 3: {
                // 查询合约
                std::cout << "请输入合约代码: ";
                std::string instrument_id;
                std::cin >> instrument_id;
                
                std::vector<CThostFtdcInstrumentField> instruments;
                if (ctp_manager.queryInstrument(instrument_id, "", instruments)) {
                    for (const auto& inst : instruments) {
                        std::cout << "合约: " << inst.InstrumentID 
                                  << " 名称: " << inst.InstrumentName
                                  << " 交易所: " << inst.ExchangeID
                                  << " 合约乘数: " << inst.VolumeMultiple << std::endl;
                    }
                } else {
                    std::cout << "查询失败" << std::endl;
                }
                break;
            }
            
            case 4: {
                // 下单
                std::string instrument_id, direction, offset_flag;
                double price;
                int volume;
                
                std::cout << "请输入合约代码: ";
                std::cin >> instrument_id;
                std::cout << "请输入价格: ";
                std::cin >> price;
                std::cout << "请输入数量: ";
                std::cin >> volume;
                std::cout << "请输入方向(buy/sell): ";
                std::cin >> direction;
                std::cout << "请输入开平标志(open/close): ";
                std::cin >> offset_flag;
                
                if (ctp_manager.placeOrder(instrument_id, price, volume, direction, offset_flag)) {
                    std::cout << "下单成功" << std::endl;
                } else {
                    std::cout << "下单失败" << std::endl;
                }
                break;
            }
            
            case 5: {
                // 撤单
                std::string order_ref, instrument_id;
                std::cout << "请输入报单引用: ";
                std::cin >> order_ref;
                std::cout << "请输入合约代码: ";
                std::cin >> instrument_id;
                
                if (ctp_manager.cancelOrder(order_ref, instrument_id)) {
                    std::cout << "撤单成功" << std::endl;
                } else {
                    std::cout << "撤单失败" << std::endl;
                }
                break;
            }
            
            case 6: {
                // 订阅行情
                std::cout << "请输入合约代码（多个用空格分隔）: ";
                std::string line;
                std::cin.ignore();
                std::getline(std::cin, line);
                
                std::vector<std::string> instruments;
                std::istringstream iss(line);
                std::string instrument;
                while (iss >> instrument) {
                    instruments.push_back(instrument);
                }
                
                if (ctp_manager.subscribeMarketData(instruments)) {
                    std::cout << "订阅请求发送成功" << std::endl;
                } else {
                    std::cout << "订阅失败" << std::endl;
                }
                break;
            }
            
            case 7: {
                // 取消订阅
                std::cout << "请输入要取消订阅的合约代码（多个用空格分隔）: ";
                std::string line;
                std::cin.ignore();
                std::getline(std::cin, line);
                
                std::vector<std::string> instruments;
                std::istringstream iss(line);
                std::string instrument;
                while (iss >> instrument) {
                    instruments.push_back(instrument);
                }
                
                if (ctp_manager.unsubscribeMarketData(instruments)) {
                    std::cout << "取消订阅成功" << std::endl;
                } else {
                    std::cout << "取消订阅失败" << std::endl;
                }
                break;
            }
            
            case 8: {
                // 查看最新行情
                std::cout << "请输入合约代码: ";
                std::string instrument_id;
                std::cin >> instrument_id;
                
                CThostFtdcDepthMarketDataField data;
                if (ctp_manager.getLatestMarketData(instrument_id, data)) {
                    std::cout << "合约: " << data.InstrumentID 
                              << " 最新价: " << data.LastPrice
                              << " 买一价: " << data.BidPrice1
                              << " 卖一价: " << data.AskPrice1
                              << " 成交量: " << data.Volume
                              << " 更新时间: " << data.UpdateTime << std::endl;
                } else {
                    std::cout << "未找到该合约的行情数据" << std::endl;
                }
                break;
            }
            
            case 9: {
                // 查看已订阅合约
                auto instruments = ctp_manager.getSubscribedInstruments();
                std::cout << "已订阅合约 (" << instruments.size() << "个): ";
                for (const auto& inst : instruments) {
                    std::cout << inst << " ";
                }
                std::cout << std::endl;
                break;
            }
            
            case 0:
                std::cout << "退出系统..." << std::endl;
                ctp_manager.stopAllServices();
                return 0;
                
            default:
                std::cout << "无效选择，请重新输入" << std::endl;
                break;
        }
        
        std::cout << "\n按回车键继续...";
        std::cin.ignore();
        std::cin.get();
    }
    
    return 0;
}
