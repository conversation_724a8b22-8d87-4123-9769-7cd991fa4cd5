#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTP WebService 直接数据传递封装
基于ctp_direct_interface的完整查询接口
"""

import ctypes
from typing import Dict, Any, Optional, List
import os
import yaml

# CTP连接配置结构体定义
class CTPConnectionConfig(ctypes.Structure):
    """CTP连接配置结构 - 对应C头文件中的CTPConnectionConfig"""
    _pack_ = 1
    _fields_ = [
        ("trade_front", ctypes.c_char * 256),      # 交易前置机地址
        ("broker_id", ctypes.c_char * 32),         # 经纪公司代码
        ("user_id", ctypes.c_char * 32),           # 用户代码
        ("password", ctypes.c_char * 32),          # 密码
        ("investor_id", ctypes.c_char * 32),       # 投资者代码
        ("app_id", ctypes.c_char * 32),            # 应用标识
        ("auth_code", ctypes.c_char * 64),         # 认证码
        ("user_product_info", ctypes.c_char * 64), # 用户端产品信息
        ("flow_dir", ctypes.c_char * 256),         # 流文件目录
    ]

# 导入结构体定义
from .ctp_structures import (
    TradingAccountField, InstrumentField,
    InvestorPositionField, TradeField, OrderField
)


from .ctp_structures import (
    INVESTORPOSITIONFIELD_FIELD_NAMES_CN,  
    TRADINGACCOUNTFIELD_FIELD_NAMES_CN,
    TRADEFIELD_FIELD_NAMES_CN, 
    ORDERFIELD_FIELD_NAMES_CN, 
    INSTRUMENTFIELD_FIELD_NAMES_CN)

CTP_FIELD_NAMES_CN = INVESTORPOSITIONFIELD_FIELD_NAMES_CN
CTP_FIELD_NAMES_CN.update(TRADINGACCOUNTFIELD_FIELD_NAMES_CN)
CTP_FIELD_NAMES_CN.update(TRADEFIELD_FIELD_NAMES_CN)
CTP_FIELD_NAMES_CN.update(ORDERFIELD_FIELD_NAMES_CN)
CTP_FIELD_NAMES_CN.update(INSTRUMENTFIELD_FIELD_NAMES_CN)

def get_field_name_cn(field_name: str) -> str:
    """获取字段的中文名称 - 不区分大小写"""
    # 先尝试直接匹配
    if field_name in CTP_FIELD_NAMES_CN:
        return CTP_FIELD_NAMES_CN[field_name]
    
    # 不区分大小写查找
    field_name_lower = field_name.lower()
    for key, value in CTP_FIELD_NAMES_CN.items():
        if key.lower() == field_name_lower:
            return value
    
    return field_name

def print_structure_fields_cn(structure_class):
    """打印结构体字段的中文对照"""
    print(f"\n{structure_class.__name__} 字段对照:")
    print("-" * 50)
    for field_name, field_type in structure_class._fields_:
        cn_name = get_field_name_cn(field_name)
        print(f"{field_name:25} -> {cn_name}")

class CTPDirectWrapper:
    """CTP服务直接数据传递封装类"""

    def __init__(self, lib_path: str = "./build/libctp_webservice.so",
                 env_config_path: str = "config/env_config.yaml",
                 trade_config_path: str = "config/trade_config.yaml"):
        """初始化CTP服务封装"""
        self.service_handle = None
        self.lib = None
        self.env_config_path = env_config_path
        self.trade_config_path = trade_config_path
        self.connection_config = None
        self.trading_config = None

        if not os.path.exists(lib_path):
            raise FileNotFoundError(f"CTP库文件不存在: {lib_path}")

        self.lib = ctypes.CDLL(lib_path)
        self._setup_function_signatures()

        # 加载配置
        self._load_configs()
    
    def _setup_function_signatures(self):
        """设置函数签名"""
        # 基础服务管理接口
        self.lib.create_ctp_service.restype = ctypes.c_void_p
        
        self.lib.initialize_service.argtypes = [ctypes.c_void_p, ctypes.POINTER(CTPConnectionConfig)]
        self.lib.initialize_service.restype = ctypes.c_bool
        
        self.lib.destroy_ctp_service.argtypes = [ctypes.c_void_p]
        
        self.lib.is_connected.argtypes = [ctypes.c_void_p]
        self.lib.is_connected.restype = ctypes.c_bool
        
        self.lib.is_logged_in.argtypes = [ctypes.c_void_p]
        self.lib.is_logged_in.restype = ctypes.c_bool
        
        # 完整查询接口
        self.lib.query_trading_account_complete.argtypes = [
            ctypes.c_void_p, 
            ctypes.POINTER(ctypes.POINTER(TradingAccountField)), 
            ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.query_trading_account_complete.restype = ctypes.c_bool
        
        # 完整交易操作接口
        self.lib.order_insert_complete.argtypes = [
            ctypes.c_void_p, 
            ctypes.c_char_p, 
            ctypes.c_double, 
            ctypes.c_int, 
            ctypes.c_char, 
            ctypes.c_char, 
            ctypes.c_char_p
        ]
        self.lib.order_insert_complete.restype = ctypes.c_bool
        
        self.lib.order_action_complete.argtypes = [
            ctypes.c_void_p, 
            ctypes.c_char_p, 
            ctypes.c_char_p, 
            ctypes.c_char_p
        ]
        self.lib.order_action_complete.restype = ctypes.c_bool

        self.lib.query_instrument_complete.argtypes = [
            ctypes.c_void_p, 
            ctypes.c_char_p, 
            ctypes.c_char_p,
            ctypes.POINTER(ctypes.POINTER(InstrumentField)), 
            ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.query_instrument_complete.restype = ctypes.c_bool
        
        self.lib.query_investor_position_complete.argtypes = [
            ctypes.c_void_p, 
            ctypes.c_char_p,
            ctypes.POINTER(ctypes.POINTER(InvestorPositionField)), 
            ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.query_investor_position_complete.restype = ctypes.c_bool
        
        self.lib.query_trade_complete.argtypes = [
            ctypes.c_void_p,
            ctypes.c_char_p,
            ctypes.POINTER(ctypes.POINTER(TradeField)),
            ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.query_trade_complete.restype = ctypes.c_bool

        self.lib.query_order_complete.argtypes = [
            ctypes.c_void_p,
            ctypes.c_char_p,
            ctypes.POINTER(ctypes.POINTER(OrderField)),
            ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.query_order_complete.restype = ctypes.c_bool


    def _load_configs(self):
        """加载配置文件"""
        # 加载环境配置
        with open(self.env_config_path, 'r', encoding='utf-8') as f:
            env_config = yaml.safe_load(f)
        
        # 加载交易配置  
        with open(self.trade_config_path, 'r', encoding='utf-8') as f:
            trade_config = yaml.safe_load(f)
        
        # 设置连接配置
        self.connection_config = env_config['ctp']
        
        # 设置交易配置
        self.trading_config = {
            **trade_config['trading']
        }
        
        self.connection_config['flow_dir'] = "./flow"

    def initialize(self) -> bool:
        """初始化CTP服务"""
        if not self.connection_config:
            print("错误: 连接配置未加载")
            return False

        self.service_handle = self.lib.create_ctp_service()
        if not self.service_handle:
            return False

        # 直接使用统一配置结构
        config = CTPConnectionConfig()
        config.trade_front = self.connection_config['trade_front'].encode('utf-8')
        config.broker_id = self.connection_config['broker_id'].encode('utf-8')
        config.user_id = self.connection_config['user_id'].encode('utf-8')
        config.password = self.connection_config['password'].encode('utf-8')
        config.investor_id = self.connection_config['investor_id'].encode('utf-8')
        config.app_id = self.connection_config['app_id'].encode('utf-8')
        config.auth_code = self.connection_config['auth_code'].encode('utf-8')
        config.user_product_info = self.connection_config['user_product_info'].encode('utf-8')
        config.flow_dir = self.connection_config['flow_dir'].encode('utf-8')

        return self.lib.initialize_service(self.service_handle, ctypes.byref(config))
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        if not self.service_handle:
            return False
        return self.lib.is_connected(self.service_handle)
    
    def is_logged_in(self) -> bool:
        """检查登录状态"""
        if not self.service_handle:
            return False
        return self.lib.is_logged_in(self.service_handle)
    
    def query_trading_account(self) -> Dict[str, Any]:
        """查询资金账户 - 使用完整查询接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}

        results_ptr = ctypes.POINTER(TradingAccountField)()
        count = ctypes.c_int()
        
        success = self.lib.query_trading_account_complete(
            self.service_handle,
            ctypes.byref(results_ptr),
            ctypes.byref(count)
        )

        if not success or count.value == 0:
            return {"success": False, "error_msg": "查询资金账户失败或无数据"}

        # 解析结果 - 使用通用解码函数
        accounts = []
        for i in range(count.value):
            account = results_ptr[i]
            account_data = self._decode_struct_fields(account)
            accounts.append(account_data)

        if accounts:
            self._print_data_table(accounts, "资金账户信息")
        
        return {"success": True, "data": accounts, "count": count.value}
    
    def query_instrument(self, instrument_id: str = "", exchange_id: str = "") -> Dict[str, Any]:
        """查询合约信息 - 使用完整查询接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}
        
        results_ptr = ctypes.POINTER(InstrumentField)()
        count = ctypes.c_int()
        
        success = self.lib.query_instrument_complete(
            self.service_handle,
            instrument_id.encode('utf-8'),
            exchange_id.encode('utf-8'),
            ctypes.byref(results_ptr),
            ctypes.byref(count)
        )
        
        if not success or count.value == 0:
            return {"success": False, "error_msg": "查询合约失败或无数据"}
        
        instruments = []
        for i in range(count.value):
            inst = results_ptr[i]            
            inst_data = self._decode_struct_fields(inst)
            instruments.append(inst_data)
        
        self._print_data_table(instruments, "合约信息")
        return {"success": True, "data": instruments, "count": count.value}

    def query_investor_position(self, instrument_id: str = "") -> Dict[str, Any]:
        """查询投资者持仓 - 使用完整查询接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}
        
        results_ptr = ctypes.POINTER(InvestorPositionField)()
        count = ctypes.c_int()
        
        success = self.lib.query_investor_position_complete(
            self.service_handle,
            instrument_id.encode('utf-8'),
            ctypes.byref(results_ptr),
            ctypes.byref(count)
        )
        
        if not success or count.value == 0:
            return {"success": True, "data": [], "count": 0}
        
        # 解析结果 - 使用通用解码函数
        positions = []
        for i in range(count.value):
            pos = results_ptr[i]
            pos_data = self._decode_struct_fields(pos)
            positions.append(pos_data)
        
        self._print_data_table(positions, "持仓信息")
        return {"success": True, "data": positions, "count": count.value}

    def query_trade(self, instrument_id: str = "") -> Dict[str, Any]:
        """查询成交信息 - 使用完整查询接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}

        results_ptr = ctypes.POINTER(TradeField)()
        count = ctypes.c_int()

        success = self.lib.query_trade_complete(
            self.service_handle,
            instrument_id.encode('utf-8'),
            ctypes.byref(results_ptr),
            ctypes.byref(count)
        )

        if not success or count.value == 0:
            return {"success": True, "data": [], "count": 0}

        trades = []
        for i in range(count.value):
            trade = results_ptr[i]
            trade_data = self._decode_struct_fields(trade)
            trades.append(trade_data)

        self._print_data_table(trades, "成交信息")
        return {"success": True, "data": trades, "count": count.value}

    def query_order(self, instrument_id: str = "") -> Dict[str, Any]:
        """查询报单信息 - 使用完整查询接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}

        results_ptr = ctypes.POINTER(OrderField)()
        count = ctypes.c_int()

        success = self.lib.query_order_complete(
            self.service_handle,
            instrument_id.encode('utf-8'),
            ctypes.byref(results_ptr),
            ctypes.byref(count)
        )

        if not success or count.value == 0:
            return {"success": True, "data": [], "count": 0}

        orders = []
        for i in range(count.value):
            order = results_ptr[i]
            order_data = self._decode_struct_fields(order)
            orders.append(order_data)

        self._print_data_table(orders, "报单信息")
        return {"success": True, "data": orders, "count": count.value}

    # 解码辅助函数
    def _safe_decode(self, value):
        """改进的安全解码函数"""
        if isinstance(value, bytes):
            # 尝试多种编码方式
            for encoding in ['gbk']:
                try:
                    decoded = value.rstrip(b'\x00').decode(encoding)
                    # 验证解码结果是否包含有效字符
                    if decoded and not all(ord(c) < 32 for c in decoded):
                        return decoded
                except (UnicodeDecodeError, UnicodeError):
                    continue
            # 如果所有编码都失败，使用错误替换
            return value.rstrip(b'\x00').decode('gbk', errors='replace')
        return str(value) if value else ''
    
    def order_insert(self, instrument_id: str, price: float, volume: int, 
                    direction: str, offset_flag: str, exchange_id: str = "") -> Dict[str, Any]:
        """下单 - 使用完整交易接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}
        
        # 转换方向参数
        direction_map = {"买": b'0', "卖": b'1'}
        if direction not in direction_map:
            return {"success": False, "error_msg": f"无效的交易方向: {direction}"}
        
        # 转换开平标志参数
        offset_map = {"开仓": b'0', "平仓": b'1', "平今": b'3', "平昨": b'4'}
        if offset_flag not in offset_map:
            return {"success": False, "error_msg": f"无效的开平标志: {offset_flag}"}
        
        success = self.lib.order_insert_complete(
            self.service_handle,
            instrument_id.encode('utf-8'),
            ctypes.c_double(price),
            ctypes.c_int(volume),
            direction_map[direction][0],
            offset_map[offset_flag][0],
            exchange_id.encode('utf-8') if exchange_id else None
        )
        
        if success:
            print(f"✅ 下单成功: {instrument_id} {direction} {offset_flag} 价格:{price} 数量:{volume}")
            return {"success": True, "message": "下单成功"}
        else:
            return {"success": False, "error_msg": "下单失败"}
    
    def order_action(self, order_ref: str, instrument_id: str, exchange_id: str = "") -> Dict[str, Any]:
        """撤单 - 使用完整交易接口"""
        if not self.service_handle:
            return {"success": False, "error_msg": "服务未初始化"}
        
        success = self.lib.order_action_complete(
            self.service_handle,
            order_ref.encode('utf-8'),
            instrument_id.encode('utf-8'),
            exchange_id.encode('utf-8') if exchange_id else None
        )
        
        if success:
            print(f"✅ 撤单成功: 报单引用:{order_ref} 合约:{instrument_id}")
            return {"success": True, "message": "撤单成功"}
        else:
            return {"success": False, "error_msg": "撤单失败"}

    def __del__(self):
        """析构函数"""
        if self.service_handle and self.lib:
            self.lib.destroy_ctp_service(self.service_handle)

    def _print_data_table(self, data_list: List[Dict[str, Any]], title: str):
        """通用数据表格打印函数 - 按结构体字段顺序纵向制表显示，支持字符串自动换行，最多6列"""
        if not data_list:
            print(f"\n📊 当前无{title}")
            return
        
        def get_display_width(text):
            """计算字符串的显示宽度，中文字符占2个位置"""
            width = 0
            for char in str(text):
                if ord(char) > 127:  # 中文字符
                    width += 2
                else:  # 英文字符
                    width += 1
            return width
        
        def wrap_text(text, max_chars=12):
            """文本自动换行，每行最多12个字符"""
            if not text:
                return [""]
            
            text = str(text)
            if len(text) <= max_chars:
                return [text]
            
            lines = []
            current_line = ""
            char_count = 0
            
            for char in text:
                char_width = 2 if ord(char) > 127 else 1
                
                if char_count + char_width > max_chars and current_line:
                    lines.append(current_line)
                    current_line = char
                    char_count = char_width
                else:
                    current_line += char
                    char_count += char_width
            
            if current_line:
                lines.append(current_line)
            
            return lines
        
        # 获取结构体字段顺序
        def get_struct_field_order(title):
            """根据标题推断结构体类型并返回字段顺序"""
            from .ctp_structures import (
                TradingAccountField, InstrumentField, InvestorPositionField,
                TradeField, OrderField
            )
            
            title_to_struct = {
                "资金账户": TradingAccountField,
                "合约": InstrumentField,
                "持仓": InvestorPositionField,
                "成交": TradeField,
                "报单": OrderField,
            }
            
            for key, struct_class in title_to_struct.items():
                if key in title:
                    return [field_name for field_name, _ in struct_class._fields_]
            
            # 如果找不到对应结构体，返回排序后的字段
            all_fields = set()
            for item in data_list:
                all_fields.update(item.keys())
            return sorted(all_fields)
        
        # 获取字段顺序
        struct_fields = get_struct_field_order(title)
        
        # 找出所有非空非零的列，按结构体顺序排列
        valid_fields = []
        for field in struct_fields:
            has_value = False
            for item in data_list:
                value = item.get(field, '')
                if value and value != 0 and value != '0' and value != '' and value != b'':
                    has_value = True
                    break
            if has_value:
                valid_fields.append(field)
        
        if not valid_fields:
            print(f"\n📊 {title}数据为空")
            return
        
        # 分批处理数据，每批最多6列
        max_cols = 6
        total_records = len(data_list)
        
        for batch_start in range(0, total_records, max_cols):
            batch_end = min(batch_start + max_cols, total_records)
            batch_data = data_list[batch_start:batch_end]
            batch_size = len(batch_data)
            
            # 表格标题
            if total_records <= max_cols:
                table_title = f"{title} (共{total_records}笔)"
            else:
                table_title = f"{title} (第{batch_start+1}-{batch_end}笔，共{total_records}笔)"
            
            # 计算字段显示名和最大宽度
            field_displays = []
            max_field_width = 0
            
            for field in valid_fields:
                cn_name = get_field_name_cn(field)
                if cn_name != field:
                    field_display = f"{field}({cn_name})"
                else:
                    field_display = field
                field_displays.append(field_display)
                max_field_width = max(max_field_width, get_display_width(field_display))
            
            # 预处理当前批次数据，计算换行后的最大行数和列宽
            wrapped_data = []
            col_widths = []
            
            for i in range(batch_size):
                item_wrapped = {}
                max_width = 8  # 最小宽度
                
                for field in valid_fields:
                    value = batch_data[i].get(field, '')
                    if isinstance(value, float):
                        value_str = f"{value:.2f}"
                    else:
                        value_str = str(value)
                    
                    # 换行处理
                    wrapped_lines = wrap_text(value_str)
                    item_wrapped[field] = wrapped_lines
                    
                    # 计算该字段所有行的最大宽度
                    for line in wrapped_lines:
                        max_width = max(max_width, get_display_width(line))
                
                wrapped_data.append(item_wrapped)
                col_widths.append(max_width)
            
            # 计算总宽度
            total_width = max_field_width + 3 + sum(col_widths) + (batch_size - 1) * 3
            
            print(f"\n{'='*total_width}")
            print(f"📊 {table_title}")
            print('='*total_width)
            
            # 打印表头
            header_field_name = "字段名"
            field_padding = max_field_width - get_display_width(header_field_name)
            header = f"{header_field_name}{' ' * field_padding} │ "
            
            for i in range(batch_size):
                col_name = f"记录{batch_start + i + 1}"
                col_padding = col_widths[i] - get_display_width(col_name)
                left_pad = col_padding // 2
                right_pad = col_padding - left_pad
                header += f"{' ' * left_pad}{col_name}{' ' * right_pad}"
                if i < batch_size - 1:
                    header += " │ "
            print(header)
            
            # 打印分隔线
            separator = "─" * max_field_width + "─┼─"
            for i in range(batch_size):
                separator += "─" * col_widths[i]
                if i < batch_size - 1:
                    separator += "─┼─"
            print(separator)
            
            # 纵向打印每个字段，支持多行显示
            for j, field in enumerate(valid_fields):
                field_display = field_displays[j]
                
                # 获取该字段在当前批次中的最大行数
                field_max_lines = max(len(wrapped_data[i][field]) for i in range(batch_size))
                
                # 逐行打印该字段的所有行
                for line_idx in range(field_max_lines):
                    if line_idx == 0:
                        # 第一行显示字段名
                        field_padding = max_field_width - get_display_width(field_display)
                        row = f"{field_display}{' ' * field_padding} │ "
                    else:
                        # 后续行显示空白
                        row = f"{' ' * max_field_width} │ "
                    
                    for i in range(batch_size):
                        wrapped_lines = wrapped_data[i][field]
                        if line_idx < len(wrapped_lines):
                            line_text = wrapped_lines[line_idx]
                        else:
                            line_text = ""
                        
                        value_padding = col_widths[i] - get_display_width(line_text)
                        left_pad = value_padding // 2
                        right_pad = value_padding - left_pad
                        row += f"{' ' * left_pad}{line_text}{' ' * right_pad}"
                        if i < batch_size - 1:
                            row += " │ "
                    
                    print(row)
            
            print('='*total_width)
            
            # 如果还有更多批次，添加分隔
            if batch_end < total_records:
                print()

    def _decode_struct_fields(self, struct_obj, debug_bytes=False):
        """改进的结构体字段解码函数 - 添加字节对齐调试"""
        result = {}
        
        print(f"\n=== 结构体解码调试 ===" if debug_bytes else "", end="")
        print(f"结构体类型: {type(struct_obj).__name__}" if debug_bytes else "", end="")
        print(f"结构体总大小: {ctypes.sizeof(struct_obj)} 字节" if debug_bytes else "", end="")
        
        # 打印原始内存内容
        raw_data = ctypes.string_at(ctypes.addressof(struct_obj), ctypes.sizeof(struct_obj))
        print(f"原始内存前64字节: {' '.join(f'{b:02X}' for b in raw_data[:64])}" if debug_bytes else "", end="")
        
        for i, (field_name, field_type) in enumerate(struct_obj._fields_):
            field_value = getattr(struct_obj, field_name)
            field_offset = getattr(struct_obj.__class__, field_name).offset
            field_size = ctypes.sizeof(field_type)

            print(f"\n字段{i}: {field_name}" if debug_bytes else "", end="")
            print(f"  偏移: {field_offset}, 大小: {field_size}, 类型: {field_type}" if debug_bytes else "", end="")
            
            # 打印该字段位置的原始字节
            field_bytes = raw_data[field_offset:field_offset + field_size]
            print(f"  原始字节: {' '.join(f'{b:02X}' for b in field_bytes)}" if debug_bytes else "", end="")
            
            if hasattr(field_type, '_length_') and field_type == ctypes.c_char * field_type._length_:
                decoded_value = self._safe_decode(field_value)
                print(f"  解码值: '{decoded_value}'" if debug_bytes else "", end="")
                result[field_name] = decoded_value
            elif field_type == ctypes.c_char:
                print(f"  字符值: {field_value}" if debug_bytes else "", end="")
                result[field_name] = field_value.decode('gbk')
            elif field_type == ctypes.c_int:
                print(f"  整数值: {field_value} (0x{field_value:08X})" if debug_bytes else "", end="")               
                result[field_name] = field_value
            elif field_type == ctypes.c_double:
                print(f"  浮点值: {field_value}" if debug_bytes else "", end="")
                result[field_name] = field_value
            else:
                print(f"  其他值: {field_value}" if debug_bytes else "", end="")
                result[field_name] = field_value
        
        return result
